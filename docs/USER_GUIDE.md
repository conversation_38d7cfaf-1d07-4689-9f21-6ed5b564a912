# LabSyncPro User Guide

This comprehensive guide will help you navigate and use the LabSyncPro Laboratory Management System effectively.

## Getting Started

### First Time Login

1. **Access the System**
   - Open your web browser and navigate to the LabSyncPro URL
   - You'll see the login page with options for different user types

2. **Demo Mode**
   - If the backend is unavailable, the system automatically switches to demo mode
   - Demo mode allows you to explore all features with sample data
   - Click "Try Demo Mode" to access the system without authentication

3. **User Authentication**
   - Enter your credentials (username/email and password)
   - Select your role: Student, Teacher, or Administrator
   - Click "Login" to access the system

## Dashboard Overview

### Student Dashboard
- **My Schedules**: View upcoming lab sessions and assignments
- **Submissions**: Access your submitted work and grades
- **Lab Status**: Check real-time lab availability
- **Profile**: Manage your account settings

### Teacher Dashboard
- **Schedule Management**: Create and manage lab sessions
- **Submissions Review**: Grade student work and provide feedback
- **Class Overview**: Monitor student progress and attendance
- **Lab Resources**: Manage lab equipment and availability

### Administrator Dashboard
- **System Overview**: Monitor overall system health and usage
- **User Management**: Add, edit, and manage user accounts
- **Lab Configuration**: Set up and configure laboratory settings
- **Reports**: Generate system reports and analytics

## Core Features

### 1. Laboratory Management

#### Viewing Lab Information
- Navigate to "Labs" section from the main menu
- View detailed information about each lab:
  - **Lab 1**: 15 computers (CL1-PC-001 to CL1-PC-015), 50 seats
  - **Lab 2**: 19 computers (CL2-PC-001 to CL2-PC-019), 50 seats
- Check real-time availability and equipment status
- View current occupancy and scheduled sessions

#### Equipment Tracking
- Monitor projectors, whiteboards, and other equipment
- Report equipment issues or maintenance needs
- View equipment usage history and schedules

### 2. Scheduling System

#### For Teachers - Creating Schedules
1. Click "Create Schedule" from the dashboard
2. Fill in the schedule details:
   - **Title**: Assignment or session name
   - **Description**: Detailed instructions
   - **Lab**: Select Lab 1 or Lab 2
   - **Date & Time**: Set start and end times
   - **Class**: Choose from available classes
   - **Type**: Select Practical, Assignment, or Exam
3. Assign students or groups to specific seats/computers
4. Save the schedule

#### For Students - Viewing Schedules
- Access "My Schedules" from the dashboard
- View upcoming sessions with details:
  - Date, time, and duration
  - Lab location and assigned seat/computer
  - Assignment instructions and requirements
  - Submission deadlines

### 3. Submission System

#### Submitting Work (Students)
1. Navigate to the assignment from "My Schedules"
2. Choose submission type:
   - **File Upload**: Submit documents, code files, or projects
   - **Text Submission**: Enter text-based answers or code
3. Add submission details:
   - Title and description
   - Upload files (if applicable)
   - Enter text content (if applicable)
4. Review and submit your work
5. View submission confirmation and timestamp

#### File Upload Guidelines
- **Supported Formats**: PDF, DOC, DOCX, TXT, ZIP, images
- **File Size Limit**: Maximum 10MB per file
- **Multiple Files**: You can upload multiple files per submission
- **Version Control**: Previous submissions are preserved

### 4. Grading System

#### For Teachers - Reviewing Submissions
1. Access "Submissions" from the teacher dashboard
2. Filter submissions by:
   - Class or student
   - Assignment or date
   - Submission status
3. Click on a submission to review:
   - View submitted files and text
   - Download files for detailed review
   - Check submission timestamp and student details

#### Grading Process
1. Open the submission for grading
2. Enter the score (0-100 or custom scale)
3. Provide detailed feedback:
   - Strengths and areas for improvement
   - Specific comments on code or content
   - Suggestions for future work
4. Save the grade and feedback
5. Optionally, notify the student of the grade

#### For Students - Viewing Grades
- Access "My Grades" from the dashboard
- View grades for all submissions with:
  - Score and percentage
  - Teacher feedback and comments
  - Submission date and grading date
  - Overall class performance (if enabled)

### 5. Group Management

#### Group Organization
- Students are automatically organized into groups of 3-4 members
- Groups are class-based and maintained throughout the semester
- View your group members and their contact information
- Collaborate on group assignments and projects

#### Group Submissions
- Some assignments may require group submissions
- Any group member can submit on behalf of the group
- All group members receive the same grade
- Individual contributions can be noted in submission comments

## User Roles and Permissions

### Student Permissions
- View assigned schedules and lab sessions
- Submit assignments and view grades
- Access lab resources and equipment information
- Update personal profile and preferences
- View group information and members

### Teacher Permissions
- Create and manage lab schedules
- Assign students to seats and computers
- Review and grade student submissions
- Provide feedback and comments
- Generate class reports and analytics
- Manage class rosters and groups

### Administrator Permissions
- Full system access and configuration
- User account management and role assignment
- Lab setup and equipment management
- System monitoring and maintenance
- Generate comprehensive reports
- Backup and restore system data

## Tips and Best Practices

### For Students
1. **Check Schedules Regularly**: Log in frequently to stay updated on assignments
2. **Submit Early**: Don't wait until the last minute to submit work
3. **Follow File Naming**: Use clear, descriptive file names
4. **Read Instructions**: Carefully review assignment requirements
5. **Ask Questions**: Contact your teacher if you need clarification

### For Teachers
1. **Clear Instructions**: Provide detailed assignment descriptions
2. **Reasonable Deadlines**: Allow sufficient time for completion
3. **Timely Feedback**: Grade submissions promptly
4. **Resource Planning**: Book labs well in advance
5. **Monitor Progress**: Check submission rates and student engagement

### For Administrators
1. **Regular Backups**: Maintain system backups and data integrity
2. **User Training**: Provide training for new users
3. **System Updates**: Keep the system updated and secure
4. **Monitor Usage**: Track system performance and user activity
5. **Support Users**: Provide technical support when needed

## Troubleshooting

### Common Issues and Solutions

#### Login Problems
- **Forgot Password**: Use the password reset feature
- **Account Locked**: Contact your administrator
- **Demo Mode**: If backend is unavailable, use demo mode to continue working

#### Submission Issues
- **File Too Large**: Compress files or split into smaller parts
- **Upload Failed**: Check internet connection and try again
- **Missing Deadline**: Contact your teacher immediately

#### Technical Problems
- **Page Not Loading**: Refresh the browser or clear cache
- **Features Not Working**: Check if you're in demo mode
- **Data Not Saving**: Ensure stable internet connection

### Getting Help
- **Student Support**: Contact your teacher or lab administrator
- **Teacher Support**: Contact the system administrator
- **Technical Issues**: Submit a support ticket or contact IT support
- **System Status**: Check the system status page for known issues

## System Requirements

### Browser Compatibility
- **Recommended**: Chrome 90+, Firefox 88+, Safari 14+, Edge 90+
- **Mobile**: Responsive design works on tablets and smartphones
- **JavaScript**: Must be enabled for full functionality

### Network Requirements
- **Internet Connection**: Stable broadband connection recommended
- **Bandwidth**: Minimum 1 Mbps for basic usage, 5 Mbps for file uploads
- **Firewall**: Ensure access to the LabSyncPro domain

---

For additional support or questions, please contact your system administrator or refer to the technical documentation.
