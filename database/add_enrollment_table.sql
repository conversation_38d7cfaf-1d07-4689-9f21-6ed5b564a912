-- Add student enrollments table to existing database
-- This table links students to classes for proper enrollment management

CREATE TABLE IF NOT EXISTS student_enrollments (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    student_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    class_id UUID NOT NULL REFERENCES classes(id) ON DELETE CASCADE,
    enrollment_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    academic_year VARCHAR(10) NOT NULL DEFAULT '2024-25', -- e.g., "2024-25"
    is_active BOOLEAN DEFAULT true,
    enrolled_by UUID REFERENCES users(id), -- Admin who enrolled the student
    notes TEXT,
    UNIQUE(student_id, class_id, academic_year)
);

-- Add index for better query performance
CREATE INDEX IF NOT EXISTS idx_student_enrollments_student_id ON student_enrollments(student_id);
CREATE INDEX IF NOT EXISTS idx_student_enrollments_class_id ON student_enrollments(class_id);
CREATE INDEX IF NOT EXISTS idx_student_enrollments_active ON student_enrollments(is_active);
CREATE INDEX IF NOT EXISTS idx_student_enrollments_academic_year ON student_enrollments(academic_year);

-- Add comment to table
COMMENT ON TABLE student_enrollments IS 'Links students to classes for enrollment management and capacity planning';
