const express = require('express');
const multer = require('multer');
const csv = require('csv-parser');
const fs = require('fs');
const path = require('path');
const bcrypt = require('bcrypt');
const { supabase } = require('../config/supabase');
const { authenticateToken, requireRole } = require('../middleware/auth');

const router = express.Router();

// Configure multer for file uploads
const upload = multer({
  dest: 'uploads/imports/',
  fileFilter: (req, file, cb) => {
    if (file.mimetype === 'text/csv' || file.originalname.endsWith('.csv')) {
      cb(null, true);
    } else {
      cb(new Error('Only CSV files are allowed'), false);
    }
  },
  limits: {
    fileSize: 10 * 1024 * 1024 // 10MB limit
  }
});

// Download template files
router.get('/templates/:type', authenticateToken, requireRole(['admin']), async (req, res) => {
  try {
    const { type } = req.params;

    // Validate template type
    const validTypes = ['students', 'computers', 'instructors'];
    if (!validTypes.includes(type)) {
      return res.status(400).json({ error: 'Invalid template type' });
    }

    // Generate CSV content based on type
    let csvContent = '';

    if (type === 'students') {
      csvContent = `first_name,last_name,email,student_id,class_id,stream,phone,address
John,Doe,<EMAIL>,CS001,class-1,Computer Science,+1234567890,123 Main St
Jane,Smith,<EMAIL>,CS002,class-1,Computer Science,+1234567891,456 Oak Ave
Bob,Johnson,<EMAIL>,IT001,class-2,Information Technology,+1234567892,789 Pine Rd`;
    } else if (type === 'computers') {
      csvContent = `computer_id,lab_id,seat_number,brand,model,specifications,status,purchase_date,warranty_expiry
CL1-PC-001,lab-1,1,Dell,OptiPlex 7090,Intel i5 8GB RAM 256GB SSD,active,2023-01-15,2026-01-15
CL1-PC-002,lab-1,2,HP,EliteDesk 800,Intel i7 16GB RAM 512GB SSD,active,2023-01-15,2026-01-15
CL2-PC-001,lab-2,1,Lenovo,ThinkCentre M720,Intel i5 8GB RAM 256GB SSD,maintenance,2023-02-01,2026-02-01`;
    } else if (type === 'instructors') {
      csvContent = `first_name,last_name,email,employee_id,department,phone,address,specialization
Dr. Alice,Wilson,<EMAIL>,EMP001,Computer Science,+1234567893,321 Faculty St,Database Systems
Prof. Bob,Davis,<EMAIL>,EMP002,Information Technology,+1234567894,654 Academic Ave,Network Security
Dr. Carol,Brown,<EMAIL>,EMP003,Computer Science,+1234567895,987 Education Blvd,Machine Learning`;
    }

    // Set headers for download
    res.setHeader('Content-Type', 'text/csv');
    res.setHeader('Content-Disposition', `attachment; filename="${type}_import_template.csv"`);
    res.send(csvContent);

  } catch (error) {
    console.error('Download template error:', error);
    res.status(500).json({ error: 'Failed to download template' });
  }
});

// Import students from CSV
router.post('/students', authenticateToken, requireRole(['admin']), upload.single('csvFile'), async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({ error: 'No CSV file uploaded' });
    }

    const results = [];
    const errors = [];
    let processedCount = 0;
    let successCount = 0;

    // Read and parse CSV file
    fs.createReadStream(req.file.path)
      .pipe(csv())
      .on('data', (data) => results.push(data))
      .on('end', async () => {
        try {
          for (const row of results) {
            processedCount++;

            try {
              // Validate required fields
              if (!row.first_name || !row.last_name || !row.email || !row.student_id) {
                errors.push(`Row ${processedCount}: Missing required fields`);
                continue;
              }

              // For demo purposes, just count as successful
              // In real implementation, you would insert into Supabase
              successCount++;
            } catch (error) {
              console.error(`Error processing row ${processedCount}:`, error);
              errors.push(`Row ${processedCount}: ${error.message}`);
            }
          }

          // Clean up uploaded file
          fs.unlinkSync(req.file.path);

          res.json({
            message: 'Student import completed',
            processed: processedCount,
            successful: successCount,
            failed: processedCount - successCount,
            errors: errors
          });

        } catch (error) {
          console.error('Import error:', error);
          // Clean up uploaded file
          if (req.file && fs.existsSync(req.file.path)) {
            fs.unlinkSync(req.file.path);
          }
          res.status(500).json({ error: 'Import failed' });
        }
      })
      .on('error', (error) => {
        console.error('CSV parsing error:', error);
        // Clean up uploaded file
        if (req.file && fs.existsSync(req.file.path)) {
          fs.unlinkSync(req.file.path);
        }
        res.status(500).json({ error: 'Failed to process CSV file' });
      });

  } catch (error) {
    console.error('Import error:', error);
    // Clean up uploaded file
    if (req.file && fs.existsSync(req.file.path)) {
      fs.unlinkSync(req.file.path);
    }
    res.status(500).json({ error: 'Import failed' });
  }
});

// Import computers from CSV
router.post('/computers', authenticateToken, requireRole(['admin']), upload.single('csvFile'), async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({ error: 'No CSV file uploaded' });
    }

    const results = [];
    const errors = [];
    let processedCount = 0;
    let successCount = 0;

    // Read and parse CSV file
    fs.createReadStream(req.file.path)
      .pipe(csv())
      .on('data', (data) => results.push(data))
      .on('end', async () => {
        try {
          for (const row of results) {
            processedCount++;

            try {
              // Validate required fields
              if (!row.computer_id || !row.lab_id || !row.seat_number) {
                errors.push(`Row ${processedCount}: Missing required fields`);
                continue;
              }

              // For demo purposes, just count as successful
              // In real implementation, you would insert into Supabase
              successCount++;
            } catch (error) {
              console.error(`Error processing row ${processedCount}:`, error);
              errors.push(`Row ${processedCount}: ${error.message}`);
            }
          }

          // Clean up uploaded file
          fs.unlinkSync(req.file.path);

          res.json({
            message: 'Computer import completed',
            processed: processedCount,
            successful: successCount,
            failed: processedCount - successCount,
            errors: errors
          });

        } catch (error) {
          console.error('Import error:', error);
          // Clean up uploaded file
          if (req.file && fs.existsSync(req.file.path)) {
            fs.unlinkSync(req.file.path);
          }
          res.status(500).json({ error: 'Import failed' });
        }
      })
      .on('error', (error) => {
        console.error('CSV parsing error:', error);
        // Clean up uploaded file
        if (req.file && fs.existsSync(req.file.path)) {
          fs.unlinkSync(req.file.path);
        }
        res.status(500).json({ error: 'Failed to process CSV file' });
      });

  } catch (error) {
    console.error('Import error:', error);
    // Clean up uploaded file
    if (req.file && fs.existsSync(req.file.path)) {
      fs.unlinkSync(req.file.path);
    }
    res.status(500).json({ error: 'Import failed' });
  }
});

// Import instructors from CSV
router.post('/instructors', authenticateToken, requireRole(['admin']), upload.single('csvFile'), async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({ error: 'No CSV file uploaded' });
    }

    const results = [];
    const errors = [];
    let processedCount = 0;
    let successCount = 0;

    // Read and parse CSV file
    fs.createReadStream(req.file.path)
      .pipe(csv())
      .on('data', (data) => results.push(data))
      .on('end', async () => {
        try {
          for (const row of results) {
            processedCount++;

            try {
              // Validate required fields
              if (!row.first_name || !row.last_name || !row.email || !row.employee_id) {
                errors.push(`Row ${processedCount}: Missing required fields`);
                continue;
              }

              // For demo purposes, just count as successful
              // In real implementation, you would insert into Supabase
              successCount++;
            } catch (error) {
              console.error(`Error processing row ${processedCount}:`, error);
              errors.push(`Row ${processedCount}: ${error.message}`);
            }
          }

          // Clean up uploaded file
          fs.unlinkSync(req.file.path);

          res.json({
            message: 'Instructor import completed',
            processed: processedCount,
            successful: successCount,
            failed: processedCount - successCount,
            errors: errors
          });

        } catch (error) {
          console.error('Import error:', error);
          // Clean up uploaded file
          if (req.file && fs.existsSync(req.file.path)) {
            fs.unlinkSync(req.file.path);
          }
          res.status(500).json({ error: 'Import failed' });
        }
      })
      .on('error', (error) => {
        console.error('CSV parsing error:', error);
        // Clean up uploaded file
        if (req.file && fs.existsSync(req.file.path)) {
          fs.unlinkSync(req.file.path);
        }
        res.status(500).json({ error: 'Failed to process CSV file' });
      });

  } catch (error) {
    console.error('Import error:', error);
    // Clean up uploaded file
    if (req.file && fs.existsSync(req.file.path)) {
      fs.unlinkSync(req.file.path);
    }
    res.status(500).json({ error: 'Import failed' });
  }
});

module.exports = router;
