const express = require('express');
const router = express.Router();
const { supabase } = require('../config/supabase');
const { getRecords } = require('../utils/supabaseHelpers');
const { authenticateToken, requireInstructor } = require('../middleware/auth');
const { body, validationResult } = require('express-validator');

// Get computer inventory for all labs
router.get('/computers', authenticateToken, async (req, res) => {
  try {
    const { labId, status, search, page = 1, limit = 50 } = req.query;
    const offset = (page - 1) * limit;

    // Use Supabase to get computer inventory
    let query = supabase
      .from('computers')
      .select('*')
      .order('computer_name');

    // Apply filters
    if (labId) {
      query = query.eq('lab_id', labId);
    }

    if (status) {
      query = query.eq('status', status);
    }

    if (search) {
      query = query.or(`computer_name.ilike.%${search}%,labs.name.ilike.%${search}%`);
    }

    // Apply pagination
    const startIndex = (parseInt(page) - 1) * parseInt(limit);
    query = query.range(startIndex, startIndex + parseInt(limit) - 1);

    const { data: computers, error, count } = await query;

    if (error) {
      console.error('Supabase error:', error);
      return res.status(500).json({ error: 'Failed to fetch computer inventory' });
    }

    // Filter by status
    if (status) {
      if (status === 'available') {
        inventoryQuery += ` AND c.status = 'functional' AND sa.id IS NULL`;
      } else if (status === 'assigned') {
        inventoryQuery += ` AND sa.id IS NOT NULL`;
      } else if (status === 'functional') {
        inventoryQuery += ` AND c.status = 'functional'`;
      } else if (status === 'in_repair') {
        inventoryQuery += ` AND c.status = 'in_repair'`;
      } else if (status === 'maintenance') {
        inventoryQuery += ` AND c.status = 'maintenance'`;
      } else if (status === 'retired') {
        inventoryQuery += ` AND c.status = 'retired'`;
      } else if (status === 'offline') {
        inventoryQuery += ` AND c.status = 'offline'`;
      }
    }

    // Map the data to match frontend expectations
    const mappedComputers = computers.map(computer => ({
      id: computer.id,
      computer_name: computer.computer_name,
      computer_number: computer.seat_number || computer.computer_name, // Use seat_number or fallback to computer_name
      specifications: computer.specifications || {},
      is_functional: computer.is_functional !== false,
      status: computer.status || 'functional',
      condition_notes: computer.condition_notes,
      last_maintenance_date: computer.last_maintenance_date,
      next_maintenance_date: computer.next_maintenance_date,
      purchase_date: computer.purchase_date,
      warranty_expiry: computer.warranty_expiry,
      created_at: computer.created_at,
      updated_at: computer.updated_at,
      lab_name: 'Unknown Lab', // Will get lab info separately
      lab_location: 'Unknown Location',
      current_status: computer.status || 'functional',
      assignment_id: null,
      current_schedule: null,
      scheduled_date: null
    }));

    // Calculate pagination
    const totalPages = Math.ceil((count || 0) / parseInt(limit));

    res.json({
      message: 'Computer inventory retrieved successfully',
      computers: mappedComputers,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total: count || 0,
        pages: totalPages
      }
    });

  } catch (error) {
    console.error('Error fetching computer inventory:', error);
    res.status(500).json({ error: 'Failed to fetch computer inventory' });
  }
});

// Get computer details by ID
router.get('/computers/:computerId', authenticateToken, async (req, res) => {
  try {
    const { computerId } = req.params;

    const { data: computer, error } = await supabase
      .from('computers')
      .select('*')
      .eq('id', computerId)
      .single();

    if (error) {
      console.error('Supabase error:', error);
      return res.status(404).json({ error: 'Computer not found' });
    }

    res.json({
      message: 'Computer details retrieved successfully',
      computer: {
        id: computer.id,
        computer_name: computer.computer_name,
        computer_number: computer.seat_number || computer.computer_name,
        specifications: computer.specifications || {},
        is_functional: computer.is_functional !== false,
        status: computer.status || 'functional',
        condition_notes: computer.condition_notes,
        last_maintenance_date: computer.last_maintenance_date,
        next_maintenance_date: computer.next_maintenance_date,
        purchase_date: computer.purchase_date,
        warranty_expiry: computer.warranty_expiry,
        created_at: computer.created_at,
        updated_at: computer.updated_at,
        lab_name: 'Unknown Lab', // Will get lab info separately
        lab_location: 'Unknown Location'
      }
    });

  } catch (error) {
    console.error('Error fetching computer details:', error);
    res.status(500).json({ error: 'Failed to fetch computer details' });
  }
});

// Get computer assignment history
router.get('/computers/:computerId/history', authenticateToken, async (req, res) => {
  try {
    const { computerId } = req.params;
    const { limit = 20 } = req.query;

    // For now, return empty history as this feature requires schedule assignments
    res.json({
      message: 'Computer assignment history retrieved successfully',
      history: [],
      pagination: {
        page: 1,
        limit: parseInt(limit),
        total: 0,
        pages: 0
      }
    });

  } catch (error) {
    console.error('Error fetching computer history:', error);
    res.status(500).json({ error: 'Failed to fetch computer assignment history' });
  }
});

module.exports = router;
