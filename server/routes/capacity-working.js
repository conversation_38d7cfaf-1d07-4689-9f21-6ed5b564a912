const express = require('express');
const { supabase } = require('../config/supabase');
const { authenticateToken } = require('../middleware/auth');

const router = express.Router();

// Get capacity data for all labs
router.get('/', authenticateToken, async (req, res) => {
  try {
    // Try to get capacity data from Supabase
    try {
      const { data: labs, error: labsError } = await supabase
        .from('labs')
        .select('id, name, capacity');

      if (labsError) {
        throw labsError;
      }

      const { data: computers, error: computersError } = await supabase
        .from('computer_inventory')
        .select('lab_id, status');

      if (computersError) {
        throw computersError;
      }

      // Calculate capacity data
      const capacityData = {
        labs: labs.map(lab => {
          const labComputers = computers.filter(comp => comp.lab_id === lab.id);
          const totalComputers = labComputers.length;
          const availableComputers = labComputers.filter(comp => comp.status === 'available').length;
          const occupiedComputers = labComputers.filter(comp => comp.status === 'occupied').length;
          const maintenanceComputers = labComputers.filter(comp => comp.status === 'maintenance').length;

          return {
            id: lab.id,
            name: lab.name,
            total_computers: totalComputers,
            available_computers: availableComputers,
            occupied_computers: occupiedComputers,
            maintenance_computers: maintenanceComputers,
            capacity_percentage: totalComputers > 0 ? Math.round((occupiedComputers / totalComputers) * 100) : 0
          };
        })
      };

      res.json({
        message: 'Capacity data retrieved successfully',
        data: capacityData
      });

    } catch (supabaseError) {
      console.log('Supabase capacity fetch failed, using sample data:', supabaseError.message);
      
      // Provide sample capacity data
      const capacityData = {
        labs: [
          {
            id: 'f202a2b2-08b0-41cf-8f97-c0160f247ad8',
            name: 'Computer Lab 1',
            total_computers: 15,
            available_computers: 5,
            occupied_computers: 10,
            maintenance_computers: 0,
            capacity_percentage: 67
          },
          {
            id: 'a8f3d1e5-2b4c-4d6e-8f9a-1c2d3e4f5a6b',
            name: 'Computer Lab 2',
            total_computers: 19,
            available_computers: 8,
            occupied_computers: 11,
            maintenance_computers: 0,
            capacity_percentage: 58
          }
        ]
      };

      res.json({
        message: 'Capacity data retrieved successfully (sample data)',
        data: capacityData
      });
    }

  } catch (error) {
    console.error('Get capacity data error:', error);
    res.status(500).json({ error: 'Failed to fetch capacity data' });
  }
});

// Get capacity data for a specific lab
router.get('/:labId', authenticateToken, async (req, res) => {
  try {
    const { labId } = req.params;

    // Try to get specific lab capacity from Supabase
    try {
      const { data: lab, error: labError } = await supabase
        .from('labs')
        .select('id, name, capacity')
        .eq('id', labId)
        .single();

      if (labError) {
        throw labError;
      }

      const { data: computers, error: computersError } = await supabase
        .from('computer_inventory')
        .select('id, computer_id, status')
        .eq('lab_id', labId);

      if (computersError) {
        throw computersError;
      }

      const totalComputers = computers.length;
      const availableComputers = computers.filter(comp => comp.status === 'available').length;
      const occupiedComputers = computers.filter(comp => comp.status === 'occupied').length;
      const maintenanceComputers = computers.filter(comp => comp.status === 'maintenance').length;

      const capacityData = {
        id: lab.id,
        name: lab.name,
        total_computers: totalComputers,
        available_computers: availableComputers,
        occupied_computers: occupiedComputers,
        maintenance_computers: maintenanceComputers,
        capacity_percentage: totalComputers > 0 ? Math.round((occupiedComputers / totalComputers) * 100) : 0,
        computers: computers
      };

      res.json({
        message: 'Lab capacity data retrieved successfully',
        data: capacityData
      });

    } catch (supabaseError) {
      console.log('Supabase lab capacity fetch failed, using sample data:', supabaseError.message);
      
      // Provide sample lab capacity data
      let sampleData;
      if (labId === 'f202a2b2-08b0-41cf-8f97-c0160f247ad8') {
        sampleData = {
          id: 'f202a2b2-08b0-41cf-8f97-c0160f247ad8',
          name: 'Computer Lab 1',
          total_computers: 15,
          available_computers: 5,
          occupied_computers: 10,
          maintenance_computers: 0,
          capacity_percentage: 67,
          computers: Array.from({ length: 15 }, (_, i) => ({
            id: `comp-cl1-${i + 1}`,
            computer_id: `CL1-PC-${String(i + 1).padStart(3, '0')}`,
            status: i < 10 ? 'occupied' : 'available'
          }))
        };
      } else if (labId === 'a8f3d1e5-2b4c-4d6e-8f9a-1c2d3e4f5a6b') {
        sampleData = {
          id: 'a8f3d1e5-2b4c-4d6e-8f9a-1c2d3e4f5a6b',
          name: 'Computer Lab 2',
          total_computers: 19,
          available_computers: 8,
          occupied_computers: 11,
          maintenance_computers: 0,
          capacity_percentage: 58,
          computers: Array.from({ length: 19 }, (_, i) => ({
            id: `comp-cl2-${i + 1}`,
            computer_id: `CL2-PC-${String(i + 1).padStart(3, '0')}`,
            status: i < 11 ? 'occupied' : 'available'
          }))
        };
      } else {
        sampleData = {
          id: labId,
          name: 'Demo Lab',
          total_computers: 20,
          available_computers: 10,
          occupied_computers: 10,
          maintenance_computers: 0,
          capacity_percentage: 50,
          computers: []
        };
      }

      res.json({
        message: 'Lab capacity data retrieved successfully (sample data)',
        data: sampleData
      });
    }

  } catch (error) {
    console.error('Get lab capacity data error:', error);
    res.status(500).json({ error: 'Failed to fetch lab capacity data' });
  }
});

module.exports = router;
