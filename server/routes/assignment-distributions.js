const express = require('express');
const router = express.Router();
const { supabase } = require('../config/supabase');
const { authenticateToken } = require('../middleware/auth');

// In-memory storage for mock distributions (until database table is created)
let mockDistributions = [];

// Get all assignment distributions
router.get('/', authenticateToken, async (req, res) => {
  try {
    // Try to get real data from Supabase first
    const { data: distributions, error } = await supabase
      .from('assignment_distributions')
      .select(`
        *,
        created_assignments!inner(id, name, description, pdf_filename, status),
        classes!inner(id, name)
      `)
      .order('created_at', { ascending: false });

    if (!error && distributions && distributions.length > 0) {
      // Map the data to match frontend expectations
      const mappedDistributions = distributions.map(dist => ({
        id: dist.id,
        assignmentId: dist.assignment_id,
        assignmentName: dist.created_assignments.name,
        assignmentDescription: dist.created_assignments.description,
        pdfFileName: dist.created_assignments.pdf_filename,
        classId: dist.class_id,
        className: dist.classes.name,
        assignmentType: dist.assignment_type,
        scheduledDate: dist.scheduled_date,
        deadline: dist.deadline,
        status: dist.status,
        createdAt: dist.created_at
      }));

      return res.json({
        success: true,
        distributions: mappedDistributions,
        message: `Assignment distributions retrieved successfully (${mappedDistributions.length} distributions)`
      });
    }

    // Fallback to mock distributions if no real data
    res.json({
      success: true,
      distributions: mockDistributions,
      message: `Assignment distributions retrieved successfully (${mockDistributions.length} mock distributions)`
    });

  } catch (error) {
    console.error('Error fetching assignment distributions:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch assignment distributions'
    });
  }
});

// Create new assignment distribution
router.post('/', authenticateToken, async (req, res) => {
  try {
    const {
      assignmentId,
      classId,
      assignmentType,
      groupIds,
      userIds,
      scheduledDate,
      deadline
    } = req.body;

    if (!assignmentId || !classId || !assignmentType || !scheduledDate || !deadline) {
      return res.status(400).json({
        success: false,
        error: 'Missing required fields'
      });
    }

    // Fetch assignment details to create a complete distribution record
    let assignmentName = 'Unknown Assignment';
    let assignmentDescription = '';
    let pdfFileName = '';

    try {
      const { data: assignment, error: assignmentError } = await supabase
        .from('created_assignments')
        .select('name, description, pdf_file_name')
        .eq('id', assignmentId)
        .single();

      if (!assignmentError && assignment) {
        assignmentName = assignment.name;
        assignmentDescription = assignment.description;
        pdfFileName = assignment.pdf_file_name;
      }
    } catch (err) {
      console.log('Could not fetch assignment details:', err.message);
    }

    // Fetch class details
    let className = 'Unknown Class';
    try {
      const { data: classData, error: classError } = await supabase
        .from('classes')
        .select('name')
        .eq('id', classId)
        .single();

      if (!classError && classData) {
        className = classData.name;
      }
    } catch (err) {
      console.log('Could not fetch class details:', err.message);
    }

    // Try to save to real database first
    try {
      const { data: newDistribution, error: insertError } = await supabase
        .from('assignment_distributions')
        .insert({
          assignment_id: assignmentId,
          class_id: classId,
          assignment_type: assignmentType,
          group_ids: groupIds || [],
          user_ids: userIds || [],
          scheduled_date: scheduledDate,
          deadline: deadline,
          status: 'scheduled'
        })
        .select()
        .single();

      if (!insertError && newDistribution) {
        // Successfully saved to database
        const distributionResponse = {
          id: newDistribution.id,
          assignmentId: assignmentId,
          assignmentName: assignmentName,
          assignmentDescription: assignmentDescription,
          pdfFileName: pdfFileName,
          classId: classId,
          className: className,
          assignmentType: assignmentType,
          groupId: groupIds && groupIds.length > 0 ? groupIds[0] : null,
          groupName: assignmentType === 'group' ? 'Sample Group' : null,
          userId: userIds && userIds.length > 0 ? userIds[0] : null,
          studentName: assignmentType === 'individual' ? 'Sample Student' : null,
          scheduledDate: scheduledDate,
          deadline: deadline,
          status: 'scheduled',
          assignedAt: newDistribution.created_at,
          instructorName: req.user.firstName + ' ' + req.user.lastName
        };

        return res.status(201).json({
          success: true,
          distribution: distributionResponse,
          message: 'Assignment distribution created successfully'
        });
      }
    } catch (dbError) {
      console.log('Database save failed, using mock storage:', dbError.message);
    }

    // Fallback to mock storage
    const mockDistribution = {
      id: `mock-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      assignmentId: assignmentId,
      assignmentName: assignmentName,
      assignmentDescription: assignmentDescription,
      pdfFileName: pdfFileName,
      classId: classId,
      className: className,
      assignmentType: assignmentType,
      groupId: groupIds && groupIds.length > 0 ? groupIds[0] : null,
      groupName: assignmentType === 'group' ? 'Sample Group' : null,
      userId: userIds && userIds.length > 0 ? userIds[0] : null,
      studentName: assignmentType === 'individual' ? 'Sample Student' : null,
      scheduledDate: scheduledDate,
      deadline: deadline,
      status: 'scheduled',
      assignedAt: new Date().toISOString(),
      instructorName: req.user.firstName + ' ' + req.user.lastName
    };

    // Store in mock distributions array
    mockDistributions.push(mockDistribution);

    res.status(201).json({
      success: true,
      distribution: mockDistribution,
      message: 'Assignment distribution created successfully (mock response - table not created yet)'
    });

  } catch (error) {
    console.error('Error creating assignment distributions:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to create assignment distributions'
    });
  }
});

// Update assignment distribution
router.put('/:id', authenticateToken, async (req, res) => {
  try {
    const { id } = req.params;
    const { scheduledDate, deadline, status } = req.body;

    // Find and update the distribution in mock storage
    const distributionIndex = mockDistributions.findIndex(d => d.id === id);

    if (distributionIndex === -1) {
      return res.status(404).json({
        success: false,
        error: 'Assignment distribution not found'
      });
    }

    // Update the distribution
    mockDistributions[distributionIndex] = {
      ...mockDistributions[distributionIndex],
      scheduledDate: scheduledDate || mockDistributions[distributionIndex].scheduledDate,
      deadline: deadline || mockDistributions[distributionIndex].deadline,
      status: status || mockDistributions[distributionIndex].status,
      assignedAt: new Date().toISOString()
    };

    res.json({
      success: true,
      distribution: mockDistributions[distributionIndex],
      message: 'Assignment distribution updated successfully (mock response - table not created yet)'
    });
  } catch (error) {
    console.error('Error updating assignment distribution:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to update assignment distribution'
    });
  }
});

// Delete assignment distribution
router.delete('/:id', authenticateToken, async (req, res) => {
  try {
    const { id } = req.params;

    // Find and remove the distribution from mock storage
    const distributionIndex = mockDistributions.findIndex(d => d.id === id);

    if (distributionIndex === -1) {
      return res.status(404).json({
        success: false,
        error: 'Assignment distribution not found'
      });
    }

    // Remove the distribution
    mockDistributions.splice(distributionIndex, 1);

    res.json({
      success: true,
      message: 'Assignment distribution deleted successfully (mock response - table not created yet)'
    });
  } catch (error) {
    console.error('Error deleting assignment distribution:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to delete assignment distribution'
    });
  }
});

module.exports = router;
