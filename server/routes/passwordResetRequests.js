const express = require('express');
const { body, validationResult } = require('express-validator');
const { supabase } = require('../config/supabase');
const { authenticateToken, requireRole } = require('../middleware/auth');
const bcrypt = require('bcryptjs');

const router = express.Router();

// Get all password reset requests (admin only)
router.get('/', authenticateToken, requireRole(['admin']), async (req, res) => {
  try {
    const { status = 'all', limit = 50, offset = 0 } = req.query;
    
    let whereClause = '';
    let queryParams = [limit, offset];
    
    if (status !== 'all') {
      whereClause = 'WHERE prr.status = $3';
      queryParams.push(status);
    }
    
    // TODO: Implement password reset requests table in Supabase
    // For now, return empty data to prevent 500 errors
    res.json({
      requests: [],
      total: 0,
      limit: parseInt(limit),
      offset: parseInt(offset)
    });
    
  } catch (error) {
    console.error('Error fetching password reset requests:', error);
    res.status(500).json({ error: 'Failed to fetch password reset requests' });
  }
});

// Get password reset request statistics
router.get('/stats', authenticateToken, requireRole(['admin']), async (req, res) => {
  try {
    // For now, return mock stats since the table doesn't exist yet
    // TODO: Implement actual stats when password_reset_requests table is created
    const stats = {
      pending: 0,
      completed: 0,
      rejected: 0,
      total: 0
    };

    res.json({ stats });

  } catch (error) {
    console.error('Error fetching password reset request stats:', error);
    res.status(500).json({ error: 'Failed to fetch stats' });
  }
});

// Complete a password reset request
router.post('/:requestId/complete', [
  body('newPassword').isLength({ min: 6 }).withMessage('Password must be at least 6 characters long')
], authenticateToken, requireRole(['admin']), async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const { requestId } = req.params;
    const { newPassword } = req.body;

    // For now, return mock success response since the table doesn't exist yet
    // TODO: Implement actual password reset logic when password_reset_requests table is created
    res.json({
      message: 'Password reset completed successfully (mock response - table not created yet)',
      userEmail: '<EMAIL>',
      userName: 'Mock User'
    });

  } catch (error) {
    console.error('Error completing password reset:', error);
    res.status(500).json({ error: 'Failed to complete password reset' });
  }
});

// Reject a password reset request
router.post('/:requestId/reject', authenticateToken, requireRole(['admin']), async (req, res) => {
  try {
    const { requestId } = req.params;

    // For now, return mock success response since the table doesn't exist yet
    // TODO: Implement actual reject logic when password_reset_requests table is created
    res.json({
      message: 'Password reset request rejected (mock response - table not created yet)',
      userEmail: '<EMAIL>',
      userName: 'Mock User'
    });

  } catch (error) {
    console.error('Error rejecting password reset:', error);
    res.status(500).json({ error: 'Failed to reject password reset request' });
  }
});

// Delete a password reset request (admin only)
router.delete('/:requestId', authenticateToken, requireRole(['admin']), async (req, res) => {
  try {
    const { requestId } = req.params;

    // For now, return mock success response since the table doesn't exist yet
    // TODO: Implement actual delete logic when password_reset_requests table is created
    res.json({ message: 'Password reset request deleted successfully (mock response - table not created yet)' });

  } catch (error) {
    console.error('Error deleting password reset request:', error);
    res.status(500).json({ error: 'Failed to delete password reset request' });
  }
});

module.exports = router;
