const express = require('express');
const router = express.Router();
const bcrypt = require('bcrypt');
const { supabase } = require('../config/supabase');
const { authenticateToken, requireRole, requireAdmin } = require('../middleware/auth');
// Temporarily comment out enhanced services
// const { applyRateLimit } = require('../middleware/rateLimiter');
// const DatabaseService = require('../services/databaseService');
// const AuditService = require('../services/auditService');
// const SessionService = require('../services/sessionService');
// const TwoFactorService = require('../services/twoFactorService');
// const MonitoringService = require('../services/monitoringService');
// const BackupService = require('../services/backupService');
const path = require('path');
const fs = require('fs');
const { exec } = require('child_process');
const util = require('util');
const execAsync = util.promisify(exec);

// Get all assignment submissions for admin view
router.get('/assignment-submissions', authenticateToken, requireRole(['admin', 'instructor']), async (req, res) => {
  try {
    // Return sample data for now
    const sampleSubmissions = [
      {
        id: '1',
        assignmentDistributionId: 'dist-1',
        userId: 'user-1',
        assignmentResponseFilename: 'assignment_response.pdf',
        outputTestFilename: 'output_test.pdf',
        submittedAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        isLocked: false,
        assignmentTitle: 'Sample Assignment',
        assignmentDescription: 'This is a sample assignment',
        className: 'Computer Science 101',
        assignmentType: 'individual',
        deadline: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
        scheduledDate: new Date().toISOString(),
        studentName: 'John Doe',
        studentEmail: '<EMAIL>',
        studentId: 'CS001',
        groupName: null,
        grade: {
          id: 'grade-1',
          score: 85,
          maxScore: 100,
          gradeLetter: 'B',
          feedback: 'Good work!',
          gradedAt: new Date().toISOString()
        }
      }
    ];

    res.json({
      message: 'Assignment submissions retrieved successfully',
      submissions: sampleSubmissions,
      total: sampleSubmissions.length,
      pagination: {
        page: 1,
        limit: 50,
        total: sampleSubmissions.length,
        pages: 1
      }
    });

  } catch (error) {
    console.error('Error fetching assignment submissions:', error);
    res.status(500).json({ error: 'Failed to fetch assignment submissions' });
  }
});

// Get submission statistics for admin dashboard
router.get('/assignment-submissions/stats', authenticateToken, requireRole(['admin', 'instructor']), async (req, res) => {
  try {
    // Return sample statistics
    const stats = {
      total_submissions: 25,
      completed_submissions: 20,
      partial_submissions: 3,
      no_submissions: 2,
      overdue_submissions: 1
    };

    res.json({
      message: 'Submission statistics retrieved successfully',
      stats: stats
    });
  } catch (error) {
    console.error('Error fetching submission statistics:', error);
    res.status(500).json({ error: 'Failed to fetch submission statistics' });
  }
});

// Git status endpoint
router.get('/git/status', authenticateToken, requireAdmin, async (req, res) => {
  try {
    // Check if we're in a git repository
    const { stdout: gitStatus } = await execAsync('git status --porcelain');
    const { stdout: currentBranch } = await execAsync('git branch --show-current');
    const { stdout: lastCommit } = await execAsync('git log -1 --pretty=format:"%h - %s (%cr)"');

    const changes = gitStatus.trim().split('\n').filter(line => line.trim());

    res.json({
      hasChanges: changes.length > 0,
      changes: changes,
      currentBranch: currentBranch.trim(),
      lastCommit: lastCommit.trim(),
      status: 'success'
    });
  } catch (error) {
    console.error('Git status error:', error);
    // Return mock data if git is not available
    res.json({
      hasChanges: false,
      changes: [],
      currentBranch: 'main',
      lastCommit: 'No commits yet',
      status: 'git_not_available'
    });
  }
});

// Git push endpoint
router.post('/git/push', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const { commitMessage } = req.body;

    if (!commitMessage || !commitMessage.trim()) {
      return res.status(400).json({ error: 'Commit message is required' });
    }

    // Add all changes
    await execAsync('git add .');

    // Commit changes
    await execAsync(`git commit -m "${commitMessage.trim()}"`);

    // Push to origin
    const { stdout: pushOutput } = await execAsync('git push origin main');

    // Get updated status
    const { stdout: lastCommit } = await execAsync('git log -1 --pretty=format:"%h - %s (%cr)"');

    res.json({
      success: true,
      message: 'Changes pushed to GitHub successfully',
      pushOutput: pushOutput,
      lastCommit: lastCommit.trim(),
      hasChanges: false
    });
  } catch (error) {
    console.error('Git push error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to push changes to GitHub',
      message: error.message
    });
  }
});

// Git test and push endpoint
router.post('/git/test-and-push', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const { commitMessage } = req.body;

    if (!commitMessage || !commitMessage.trim()) {
      return res.status(400).json({ error: 'Commit message is required' });
    }

    // Run tests first (if test script exists)
    let testResults = { success: true, output: 'No tests configured' };
    try {
      const { stdout: testOutput } = await execAsync('npm test');
      testResults = { success: true, output: testOutput };
    } catch (testError) {
      testResults = { success: false, output: testError.message };
      return res.json({
        success: false,
        message: 'Tests failed - push cancelled',
        testResults: testResults
      });
    }

    // If tests pass, proceed with git operations
    await execAsync('git add .');
    await execAsync(`git commit -m "${commitMessage.trim()}"`);
    const { stdout: pushOutput } = await execAsync('git push origin main');
    const { stdout: lastCommit } = await execAsync('git log -1 --pretty=format:"%h - %s (%cr)"');

    res.json({
      success: true,
      message: 'Tests passed and changes pushed to GitHub successfully',
      pushOutput: pushOutput,
      lastCommit: lastCommit.trim(),
      hasChanges: false,
      testResults: testResults
    });
  } catch (error) {
    console.error('Git test and push error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to test and push changes',
      message: error.message
    });
  }
});

// Add demo students to all classes
router.post('/add-demo-students', authenticateToken, async (req, res) => {
  try {
    console.log('Starting demo student creation...');

    // First, get all classes
    const { data: classes, error: classesError } = await supabase
      .from('classes')
      .select('id, name');

    if (classesError) {
      console.error('Error fetching classes:', classesError);
      return res.status(500).json({ error: 'Failed to fetch classes' });
    }

    if (!classes || classes.length === 0) {
      return res.status(404).json({ error: 'No classes found' });
    }

    console.log(`Found ${classes.length} classes`);

    const studentsCreated = [];
    const errors = [];

    // For each class, create 5 demo students
    for (const classItem of classes) {
      console.log(`Creating students for class: ${classItem.name}`);

      // Clean class name for student naming (remove spaces and special chars)
      const cleanClassName = classItem.name.replace(/[^a-zA-Z0-9]/g, '').toLowerCase();

      for (let i = 1; i <= 5; i++) {
        try {
          const studentName = `${cleanClassName}_${i}_student`;
          const email = `${studentName}@student.edu`;
          const studentId = `2024${String(Math.floor(Math.random() * 10000)).padStart(4, '0')}`;

          // Hash password (using bcrypt)
          const hashedPassword = await bcrypt.hash('password123', 10);

          const { data: newStudent, error: studentError } = await supabase
            .from('users')
            .insert({
              email: email,
              password_hash: hashedPassword,
              first_name: studentName.split('_')[0] + '_' + studentName.split('_')[1],
              last_name: 'Student',
              role: 'student',
              student_id: studentId,
              is_active: true
            })
            .select()
            .single();

          if (studentError) {
            console.error(`Error creating student ${studentName}:`, studentError);
            errors.push(`Failed to create ${studentName}: ${studentError.message}`);
          } else {
            console.log(`✅ Created student: ${studentName} (${email})`);
            studentsCreated.push({
              name: studentName,
              email: email,
              studentId: studentId,
              className: classItem.name,
              id: newStudent.id
            });
          }
        } catch (error) {
          console.error(`Error creating student ${i} for class ${classItem.name}:`, error);
          errors.push(`Failed to create student ${i} for ${classItem.name}: ${error.message}`);
        }
      }
    }

    console.log(`Demo student creation completed. Created: ${studentsCreated.length}, Errors: ${errors.length}`);

    res.json({
      message: 'Demo students creation completed',
      studentsCreated: studentsCreated.length,
      totalClasses: classes.length,
      students: studentsCreated,
      errors: errors
    });

  } catch (error) {
    console.error('Add demo students error:', error);
    res.status(500).json({ error: 'Failed to add demo students', details: error.message });
  }
});

// Enroll student in class (creates user account if needed)
router.post('/enroll-student', authenticateToken, async (req, res) => {
  try {
    const {
      firstName,
      lastName,
      email,
      studentId,
      classId,
      academicYear = '2024-25',
      password = 'student123' // Default password
    } = req.body;

    console.log('Enrolling student:', { firstName, lastName, email, studentId, classId });

    // Validate required fields
    if (!firstName || !lastName || !email || !studentId || !classId) {
      return res.status(400).json({
        error: 'Missing required fields: firstName, lastName, email, studentId, classId'
      });
    }

    // Check if user already exists
    const { data: existingUser, error: userCheckError } = await supabase
      .from('users')
      .select('id, email, student_id')
      .or(`email.eq.${email},student_id.eq.${studentId}`)
      .single();

    let userId;

    if (existingUser) {
      // User already exists, use existing user
      userId = existingUser.id;
      console.log('Using existing user:', existingUser.email);
    } else {
      // Create new user account
      const hashedPassword = await bcrypt.hash(password, 10);

      const { data: newUser, error: createUserError } = await supabase
        .from('users')
        .insert({
          email: email,
          password_hash: hashedPassword,
          first_name: firstName,
          last_name: lastName,
          role: 'student',
          student_id: studentId,
          is_active: true
        })
        .select()
        .single();

      if (createUserError) {
        console.error('Error creating user:', createUserError);
        return res.status(500).json({
          error: 'Failed to create user account',
          details: createUserError.message
        });
      }

      userId = newUser.id;
      console.log('✅ Created new user account:', newUser.email);
    }

    // Check if student is already enrolled in this class
    const { data: existingEnrollment, error: enrollmentCheckError } = await supabase
      .from('student_enrollments')
      .select('id')
      .eq('student_id', userId)
      .eq('class_id', classId)
      .eq('academic_year', academicYear)
      .eq('is_active', true)
      .single();

    if (existingEnrollment) {
      return res.status(400).json({
        error: 'Student is already enrolled in this class for the current academic year'
      });
    }

    // Create enrollment record
    const { data: enrollment, error: enrollmentError } = await supabase
      .from('student_enrollments')
      .insert({
        student_id: userId,
        class_id: classId,
        academic_year: academicYear,
        enrolled_by: req.user?.id || null,
        notes: `Enrolled via admin interface`
      })
      .select()
      .single();

    if (enrollmentError) {
      console.error('Error creating enrollment:', enrollmentError);
      return res.status(500).json({
        error: 'Failed to enroll student',
        details: enrollmentError.message
      });
    }

    console.log('✅ Student enrolled successfully:', enrollment.id);

    res.json({
      message: 'Student enrolled successfully',
      enrollment: {
        id: enrollment.id,
        studentId: studentId,
        email: email,
        firstName: firstName,
        lastName: lastName,
        classId: classId,
        academicYear: academicYear,
        userId: userId
      }
    });

  } catch (error) {
    console.error('Enroll student error:', error);
    res.status(500).json({ error: 'Failed to enroll student', details: error.message });
  }
});

// Enroll existing demo students in their respective classes
router.post('/enroll-demo-students', authenticateToken, async (req, res) => {
  try {
    console.log('Starting demo student enrollment...');

    // Get all classes
    const { data: classes, error: classesError } = await supabase
      .from('classes')
      .select('id, name');

    if (classesError) {
      console.error('Error fetching classes:', classesError);
      return res.status(500).json({ error: 'Failed to fetch classes' });
    }

    // Get all demo students
    const { data: demoStudents, error: studentsError } = await supabase
      .from('users')
      .select('id, first_name, last_name, email, student_id')
      .eq('role', 'student')
      .like('email', '%<EMAIL>');

    if (studentsError) {
      console.error('Error fetching demo students:', studentsError);
      return res.status(500).json({ error: 'Failed to fetch demo students' });
    }

    console.log(`Found ${demoStudents.length} demo students and ${classes.length} classes`);

    const enrollments = [];
    const errors = [];

    // Map students to classes based on their naming pattern
    for (const student of demoStudents) {
      try {
        // Extract class identifier from student name (e.g., "11nma_1" -> "11nma")
        const nameParts = student.first_name.split('_');
        if (nameParts.length >= 2) {
          const classIdentifier = nameParts[0]; // e.g., "11nma"

          // Find matching class
          const matchingClass = classes.find(cls => {
            const cleanClassName = cls.name.replace(/[^a-zA-Z0-9]/g, '').toLowerCase();
            return cleanClassName === classIdentifier;
          });

          if (matchingClass) {
            // Check if already enrolled
            const { data: existingEnrollment } = await supabase
              .from('student_enrollments')
              .select('id')
              .eq('student_id', student.id)
              .eq('class_id', matchingClass.id)
              .eq('academic_year', '2024-25')
              .eq('is_active', true)
              .single();

            if (!existingEnrollment) {
              // Create enrollment
              const { data: enrollment, error: enrollmentError } = await supabase
                .from('student_enrollments')
                .insert({
                  student_id: student.id,
                  class_id: matchingClass.id,
                  academic_year: '2024-25',
                  enrolled_by: req.user?.id || null,
                  notes: 'Auto-enrolled demo student'
                })
                .select()
                .single();

              if (enrollmentError) {
                console.error(`Error enrolling ${student.email}:`, enrollmentError);
                errors.push(`Failed to enroll ${student.email}: ${enrollmentError.message}`);
              } else {
                console.log(`✅ Enrolled ${student.email} in ${matchingClass.name}`);
                enrollments.push({
                  studentEmail: student.email,
                  studentName: `${student.first_name} ${student.last_name}`,
                  className: matchingClass.name,
                  enrollmentId: enrollment.id
                });
              }
            } else {
              console.log(`⏭️ ${student.email} already enrolled in ${matchingClass.name}`);
            }
          } else {
            console.log(`⚠️ No matching class found for student ${student.email} (identifier: ${classIdentifier})`);
            errors.push(`No matching class found for ${student.email}`);
          }
        } else {
          console.log(`⚠️ Invalid student name format: ${student.first_name}`);
          errors.push(`Invalid name format for ${student.email}`);
        }
      } catch (error) {
        console.error(`Error processing student ${student.email}:`, error);
        errors.push(`Error processing ${student.email}: ${error.message}`);
      }
    }

    console.log(`Demo student enrollment completed. Enrolled: ${enrollments.length}, Errors: ${errors.length}`);

    res.json({
      message: 'Demo student enrollment completed',
      enrollmentsCreated: enrollments.length,
      totalStudents: demoStudents.length,
      enrollments: enrollments,
      errors: errors
    });

  } catch (error) {
    console.error('Enroll demo students error:', error);
    res.status(500).json({ error: 'Failed to enroll demo students', details: error.message });
  }
});

module.exports = router;
