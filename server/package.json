{"name": "labsyncpro-server", "version": "1.0.0", "description": "Backend server for LabSyncPro", "main": "index.js", "scripts": {"start": "node index.js", "dev": "nodemon index.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:verbose": "jest --verbose", "test:auth": "jest tests/auth.test.js", "test:dashboard": "jest tests/dashboard.test.js", "test:labs": "jest tests/labs.test.js", "test:capacity": "jest tests/capacity.test.js", "test:classes": "jest tests/classes.test.js", "db:migrate": "node scripts/migrate.js", "db:seed": "node scripts/seed.js"}, "dependencies": {"@supabase/supabase-js": "^2.52.1", "bcrypt": "^6.0.0", "bcryptjs": "^2.4.3", "cors": "^2.8.5", "csv-parser": "^3.2.0", "dotenv": "^16.6.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "form-data": "^4.0.4", "helmet": "^7.1.0", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.525.0", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "node-fetch": "^3.3.2", "nodemailer": "^7.0.4", "pg": "^8.11.3", "qrcode": "^1.5.3", "speakeasy": "^2.0.0", "uuid": "^9.0.1"}, "devDependencies": {"jest": "^29.7.0", "nodemon": "^3.0.2", "supertest": "^6.3.3"}, "keywords": ["laboratory", "management", "api", "education"], "author": "LabSyncPro Team", "license": "MIT"}