{"name": "labsyncpro", "version": "1.0.0", "description": "Laboratory Management System for Computer Labs", "main": "server/index.js", "scripts": {"dev": "concurrently \"npm run server\" \"npm run client\"", "server": "cd server && npm run dev", "client": "cd client && npm run dev", "build": "cd client && npm install && npm run build", "build:server": "cd server && npm install", "start": "cd server && npm start", "install-server": "cd server && npm install", "install-client": "cd client && npm install", "install-all": "npm run install-server && npm run install-client", "test": "cd client && npm test", "test:server": "cd server && npm test", "test:client": "cd client && npm test", "test:all": "npm run test:client && npm run test:server"}, "keywords": ["laboratory", "management", "education", "assignments", "students", "react", "nodejs", "postgresql", "supabase", "text-submissions"], "author": "LabSyncPro Team", "license": "MIT", "devDependencies": {"concurrently": "^8.2.2"}, "dependencies": {"@supabase/supabase-js": "^2.52.1", "dotenv": "^17.2.1", "pg": "^8.16.3", "uuid": "^11.1.0"}, "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}, "repository": {"type": "git", "url": "https://github.com/yourusername/LabSyncPro.git"}}