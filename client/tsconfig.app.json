{
  "compilerOptions": {
    "tsBuildInfoFile": "./node_modules/.tmp/tsconfig.app.tsbuildinfo",
    "target": "ES2022",
    "useDefineForClassFields": true,
    "lib": ["ES2022", "DOM", "DOM.Iterable"],
    "module": "ESNext",
    "skipLibCheck": true,

    /* Bundler mode */
    "moduleResolution": "bundler",
    "allowImportingTsExtensions": true,
    "verbatimModuleSyntax": false,
    "moduleDetection": "force",
    "noEmit": true,
    "jsx": "react-jsx",

    /* Linting */
    "strict": false,
    "noUnusedLocals": false,
    "noUnusedParameters": false,
    "erasableSyntaxOnly": true,
    "noFallthroughCasesInSwitch": false,
    "noUncheckedSideEffectImports": false
  },
  "include": ["src"],
  "exclude": [
    "src/test/**/*",
    "src/components/timetable/PeriodTimeManager.tsx",
    "src/components/timetable/PeriodConfigurationModal.tsx",
    "src/components/timetable/TimetableVersionManager.tsx",
    "src/components/timetable/TimetableExport.tsx",
    "src/components/timetable/Timetable.tsx",
    "src/components/timetable/WeeklyClassScheduleModal.tsx",
    "src/components/student/StudentSubmissions.tsx"
  ]
}
