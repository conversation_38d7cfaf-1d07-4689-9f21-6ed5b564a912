.timetable-container {
  padding: 20px;
  background: #f8fafc;
  min-height: 100vh;
}

.timetable-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  background: white;
  padding: 20px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.timetable-title {
  display: flex;
  align-items: center;
  gap: 12px;
}

.title-icon {
  color: #3b82f6;
}

.timetable-title h1 {
  margin: 0;
  color: #1e293b;
  font-size: 24px;
  font-weight: 600;
}

.timetable-controls {
  display: flex;
  align-items: center;
  gap: 16px;
}

.week-navigation {
  display: flex;
  align-items: center;
  gap: 12px;
}

.current-week {
  font-weight: 500;
  color: #475569;
  min-width: 200px;
  text-align: center;
}

.timetable-grid-container {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.timetable-grid {
  display: grid;
  grid-template-columns: 200px repeat(auto-fit, minmax(150px, 1fr));
  min-width: 100%;
  overflow-x: auto;
}

.timetable-header-row {
  display: contents;
}

.day-header {
  background: #f1f5f9;
  padding: 16px 8px;
  font-weight: 600;
  color: #334155;
  border-bottom: 2px solid #e2e8f0;
  border-right: 1px solid #e2e8f0;
  position: sticky;
  left: 0;
  z-index: 10;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 120px;
  min-width: 120px;
  max-width: 120px;
}

.header-content {
  text-align: center;
  font-size: 14px;
}

.time-header {
  background: #f1f5f9;
  padding: 12px 8px;
  border-bottom: 2px solid #e2e8f0;
  border-right: 1px solid #e2e8f0;
  text-align: center;
  min-width: 150px;
}

.time-slot {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: #64748b;
}

.timetable-row {
  display: contents;
}

.sunday-row .day-cell,
.sunday-row .timetable-cell {
  background-color: #fef2f2;
}

.day-cell {
  background: #f8fafc;
  padding: 12px 8px;
  border-bottom: 1px solid #e2e8f0;
  border-right: 1px solid #e2e8f0;
  position: sticky;
  left: 0;
  z-index: 5;
  min-height: 80px;
  width: 120px;
  min-width: 120px;
  max-width: 120px;
}

.sunday-cell {
  background-color: #fef2f2 !important;
  color: #dc2626;
}

.day-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  text-align: center;
}

.day-name {
  font-weight: 600;
  color: #334155;
  font-size: 14px;
  margin-bottom: 4px;
  line-height: 1.2;
}

.day-date {
  font-size: 12px;
  color: #64748b;
  font-weight: 500;
  line-height: 1.2;
}

.sunday-cell .day-name {
  color: #dc2626;
}

.sunday-cell .day-date {
  color: #dc2626;
}

.timetable-cell {
  background: white;
  border-bottom: 1px solid #e2e8f0;
  border-right: 1px solid #e2e8f0;
  min-height: 80px;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
  overflow: hidden;
}

.timetable-cell:hover {
  background: #f8fafc;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.empty-cell {
  display: flex;
  align-items: center;
  justify-content: center;
}

.empty-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  color: #94a3b8;
  font-size: 12px;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.empty-cell:hover .empty-content {
  opacity: 1;
}

.add-icon {
  color: #3b82f6;
}

.has-entry {
  padding: 8px;
}

.entry-content {
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 4px;
  position: relative;
}

.entry-class {
  font-weight: 600;
  color: #1e293b;
  font-size: 13px;
  line-height: 1.2;
}

.entry-subject {
  font-size: 11px;
  color: #3b82f6;
  font-weight: 500;
}

.entry-instructor {
  font-size: 10px;
  color: #64748b;
}

.entry-lab {
  font-size: 10px;
  color: #059669;
  font-weight: 500;
}

.entry-actions {
  position: absolute;
  top: 4px;
  right: 4px;
  display: flex;
  gap: 4px;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.has-entry:hover .entry-actions {
  opacity: 1;
}

.action-btn {
  background: rgba(255, 255, 255, 0.9);
  border: 1px solid #e2e8f0;
  border-radius: 4px;
  padding: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.download-btn:hover {
  background: #059669;
  color: white;
  border-color: #059669;
}

.edit-btn:hover {
  background: #3b82f6;
  color: white;
  border-color: #3b82f6;
}

.delete-btn:hover {
  background: #ef4444;
  color: white;
  border-color: #ef4444;
}

.timetable-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 400px;
  gap: 16px;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #e2e8f0;
  border-top: 4px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Button styles */
.btn {
  padding: 8px 16px;
  border-radius: 6px;
  border: none;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 8px;
  text-decoration: none;
}

.btn-primary {
  background: #3b82f6;
  color: white;
}

.btn-primary:hover {
  background: #2563eb;
}

.btn-secondary {
  background: #64748b;
  color: white;
}

.btn-secondary:hover {
  background: #475569;
}

.btn-outline {
  background: transparent;
  color: #64748b;
  border: 1px solid #e2e8f0;
}

.btn-outline:hover {
  background: #f1f5f9;
  border-color: #cbd5e1;
}

/* Responsive design */
@media (max-width: 1024px) {
  .timetable-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }
  
  .timetable-controls {
    justify-content: space-between;
  }
  
  .week-navigation {
    order: -1;
  }
}

@media (max-width: 768px) {
  .timetable-container {
    padding: 12px;
  }
  
  .timetable-grid {
    grid-template-columns: 150px repeat(auto-fit, minmax(120px, 1fr));
  }
  
  .day-header {
    padding: 12px 8px;
  }
  
  .time-header {
    padding: 8px 4px;
  }
  
  .time-slot span {
    font-size: 10px;
  }
  
  .timetable-cell {
    min-height: 60px;
  }
  
  .entry-content {
    padding: 4px;
  }
  
  .entry-class {
    font-size: 11px;
  }
  
  .entry-subject,
  .entry-instructor,
  .entry-lab {
    font-size: 9px;
  }
}

/* Modal styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
}

.modal-content {
  background: white;
  border-radius: 12px;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
  max-width: 500px;
  width: 100%;
  max-height: 90vh;
  overflow-y: auto;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid #e2e8f0;
}

.modal-header h2 {
  margin: 0;
  color: #1e293b;
  font-size: 18px;
  font-weight: 600;
}

.modal-close {
  background: none;
  border: none;
  font-size: 24px;
  color: #64748b;
  cursor: pointer;
  padding: 0;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 6px;
  transition: all 0.2s ease;
}

.modal-close:hover {
  background: #f1f5f9;
  color: #334155;
}

.modal-form {
  padding: 24px;
}

.form-info {
  background: #f8fafc;
  padding: 12px 16px;
  border-radius: 8px;
  margin-bottom: 20px;
  border-left: 4px solid #3b82f6;
}

.form-info p {
  margin: 0;
  color: #475569;
  font-size: 14px;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 6px;
  color: #374151;
  font-weight: 500;
  font-size: 14px;
}

.form-group input,
.form-group select,
.form-group textarea {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
  transition: border-color 0.2s ease;
  box-sizing: border-box;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-group textarea {
  resize: vertical;
  min-height: 80px;
}

.modal-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  margin-top: 24px;
  padding-top: 20px;
  border-top: 1px solid #e2e8f0;
}

.modal-actions .btn {
  min-width: 100px;
  justify-content: center;
}
