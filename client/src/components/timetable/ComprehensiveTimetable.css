/* Comprehensive Timetable System Styles */

.comprehensive-timetable {
  padding: 20px;
  background: #f8fafc;
  min-height: 100vh;
}

/* Header Styles */
.timetable-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 30px;
  background: white;
  padding: 20px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.header-left h1 {
  margin: 0 0 10px 0;
  color: #1e293b;
  font-size: 28px;
  font-weight: 600;
}

.version-info {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.version-badge {
  background: #3b82f6;
  color: white;
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 500;
  width: fit-content;
}

.effective-date {
  color: #64748b;
  font-size: 14px;
}

.header-actions {
  display: flex;
  gap: 15px;
  align-items: center;
}

.view-toggle {
  display: flex;
  background: #f1f5f9;
  border-radius: 8px;
  padding: 4px;
}

.view-toggle .btn {
  margin: 0;
  border-radius: 6px;
  padding: 8px 16px;
  font-size: 14px;
  border: none;
  transition: all 0.2s;
}

.admin-actions {
  display: flex;
  gap: 10px;
}

/* Loading and Error States */
.comprehensive-timetable.loading,
.comprehensive-timetable.error {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 60vh;
}

.loading-spinner {
  text-align: center;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f4f6;
  border-top: 4px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 20px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error-message {
  text-align: center;
  background: white;
  padding: 40px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.error-message h3 {
  color: #ef4444;
  margin-bottom: 10px;
}

/* Timetable Content */
.timetable-content {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

/* Version Manager Modal */
.version-manager-modal {
  width: 90vw;
  max-width: 1200px;
  max-height: 90vh;
  display: flex;
  flex-direction: column;
}

.version-manager-modal .modal-header {
  flex-shrink: 0;
  padding: 20px 24px;
  border-bottom: 1px solid #e2e8f0;
  background: #f8fafc;
}

.version-manager-modal .modal-body {
  flex: 1;
  overflow-y: auto;
  padding: 20px 24px;
}

.version-manager-modal .modal-footer {
  flex-shrink: 0;
  padding: 16px 24px;
  border-top: 1px solid #e2e8f0;
  background: #f8fafc;
}

.version-actions {
  display: flex;
  gap: 15px;
  margin-bottom: 25px;
  padding-bottom: 20px;
  border-bottom: 1px solid #e2e8f0;
}

.versions-list h3 {
  margin-bottom: 20px;
  color: #1e293b;
}

.versions-table {
  overflow-x: auto;
}

.versions-table table {
  width: 100%;
  border-collapse: collapse;
}

.versions-table th,
.versions-table td {
  padding: 12px;
  text-align: left;
  border-bottom: 1px solid #e2e8f0;
}

.versions-table th {
  background: #f8fafc;
  font-weight: 600;
  color: #475569;
}

.versions-table tr.active-version {
  background: #f0f9ff;
}

.version-number {
  font-family: 'Monaco', 'Menlo', monospace;
  background: #f1f5f9;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 12px;
}

.version-info strong {
  display: block;
  margin-bottom: 4px;
}

.version-info small {
  color: #64748b;
  font-size: 12px;
}

.status-badge {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 500;
  text-transform: uppercase;
}

.status-badge.active {
  background: #dcfce7;
  color: #166534;
}

.status-badge.inactive {
  background: #f1f5f9;
  color: #64748b;
}

.schedule-counts {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.schedule-counts span {
  font-size: 12px;
  color: #64748b;
}

.action-buttons {
  display: flex;
  gap: 5px;
}

/* Create Version Modal */
.create-version-modal {
  width: 600px;
  max-width: 90vw;
}

.form-help {
  display: block;
  margin-top: 5px;
  font-size: 12px;
  color: #64748b;
}

.checkbox-label {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
}

.checkbox-label input[type="checkbox"] {
  margin: 0;
}

/* Comparison Modal */
.comparison-modal {
  width: 90vw;
  max-width: 1000px;
  max-height: 90vh;
  display: flex;
  flex-direction: column;
}

.comparison-modal .modal-header {
  flex-shrink: 0;
  padding: 20px 24px;
  border-bottom: 1px solid #e2e8f0;
  background: #f8fafc;
}

.comparison-modal .modal-body {
  flex: 1;
  overflow-y: auto;
  padding: 20px 24px;
}

.comparison-modal .modal-footer {
  flex-shrink: 0;
  padding: 16px 24px;
  border-top: 1px solid #e2e8f0;
  background: #f8fafc;
}

.comparison-summary {
  background: #f8fafc;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 25px;
}

.comparison-summary h4 {
  margin-bottom: 10px;
  color: #1e293b;
}

.period-changes table {
  width: 100%;
  border-collapse: collapse;
}

.period-changes th,
.period-changes td {
  padding: 12px;
  text-align: left;
  border-bottom: 1px solid #e2e8f0;
}

.period-changes th {
  background: #f8fafc;
  font-weight: 600;
}

.change-badge {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 500;
  text-transform: uppercase;
}

.change-badge.added {
  background: #dcfce7;
  color: #166534;
}

.change-badge.removed {
  background: #fee2e2;
  color: #991b1b;
}

.change-badge.modified {
  background: #fef3c7;
  color: #92400e;
}

.change-badge.unchanged {
  background: #f1f5f9;
  color: #64748b;
}

.change-added {
  background: #f0fdf4;
}

.change-removed {
  background: #fef2f2;
}

.change-modified {
  background: #fffbeb;
}

/* Validation Modal */
.validation-modal {
  width: 700px;
  max-width: 90vw;
}

.validation-status {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 25px;
}

.validation-status.valid {
  background: #f0fdf4;
  color: #166534;
}

.validation-status.invalid {
  background: #fef2f2;
  color: #991b1b;
}

.validation-status i {
  font-size: 24px;
}

.validation-status h4 {
  margin: 0 0 5px 0;
}

.validation-issues {
  background: #fef2f2;
  padding: 20px;
  border-radius: 8px;
}

.issue-item {
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #fecaca;
}

.issue-item:last-child {
  margin-bottom: 0;
  padding-bottom: 0;
  border-bottom: none;
}

.issue-item h5 {
  color: #991b1b;
  margin-bottom: 8px;
  font-size: 14px;
}

.issue-item ul {
  margin: 10px 0 0 20px;
}

.issue-item li {
  color: #7f1d1d;
  font-size: 13px;
  margin-bottom: 3px;
}

/* Period Manager Modal */
.period-manager-modal {
  width: 90vw;
  max-width: 1000px;
  max-height: 90vh;
  display: flex;
  flex-direction: column;
}

.period-manager-modal .modal-header {
  flex-shrink: 0;
  padding: 20px 24px;
  border-bottom: 1px solid #e2e8f0;
  background: #f8fafc;
}

.period-manager-modal .modal-body {
  flex: 1;
  overflow-y: auto;
  padding: 20px 24px;
}

.period-manager-modal .modal-footer {
  flex-shrink: 0;
  padding: 16px 24px;
  border-top: 1px solid #e2e8f0;
  background: #f8fafc;
}

.period-manager-modal .modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.period-manager-modal .version-info {
  display: flex;
  gap: 10px;
  align-items: center;
}

.active-badge {
  background: #dcfce7;
  color: #166534;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 500;
}

.periods-editor {
  margin-bottom: 30px;
}

.editor-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 25px;
  padding-bottom: 15px;
  border-bottom: 1px solid #e2e8f0;
}

.periods-list {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.period-item {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 20px;
}

.period-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.period-number {
  font-weight: 600;
  color: #1e293b;
}

.period-form .form-row {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 1fr;
  gap: 15px;
  margin-bottom: 15px;
}

.period-form .form-row:last-child {
  grid-template-columns: 1fr 1fr;
}

.duration-display {
  padding: 8px 12px;
  background: white;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  color: #374151;
  font-weight: 500;
  display: flex;
  align-items: center;
}

.periods-preview {
  background: #f8fafc;
  padding: 20px;
  border-radius: 8px;
}

.periods-preview h4 {
  margin-bottom: 15px;
  color: #1e293b;
}

.schedule-timeline {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.timeline-item {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 12px;
  border-radius: 6px;
  border-left: 4px solid;
}

.timeline-item.lecture-period {
  background: #eff6ff;
  border-left-color: #3b82f6;
}

.timeline-item.break-period {
  background: #f0fdf4;
  border-left-color: #10b981;
}

.time-range {
  font-family: 'Monaco', 'Menlo', monospace;
  font-size: 13px;
  color: #64748b;
  min-width: 140px;
}

.period-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.period-info strong {
  color: #1e293b;
  font-size: 14px;
}

.period-info .duration {
  color: #64748b;
  font-size: 12px;
}

/* WEF Modal */
.wef-modal {
  width: 600px;
  max-width: 90vw;
}

.wef-explanation {
  background: #fef3c7;
  padding: 15px;
  border-radius: 6px;
  margin-bottom: 20px;
  border-left: 4px solid #f59e0b;
}

.wef-explanation p {
  margin: 0;
  color: #92400e;
  font-size: 14px;
}

.version-control-info {
  background: #f0f9ff;
  padding: 15px;
  border-radius: 6px;
  margin-top: 20px;
}

.version-control-info h4 {
  margin-bottom: 10px;
  color: #1e40af;
  font-size: 14px;
}

.version-control-info ul {
  margin: 0;
  padding-left: 20px;
}

.version-control-info li {
  color: #1e40af;
  font-size: 13px;
  margin-bottom: 3px;
}

/* Weekly View Styles */
.timetable-weekly-view {
  padding: 20px;
}

.week-navigation {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 25px;
  padding-bottom: 15px;
  border-bottom: 1px solid #e2e8f0;
}

.nav-controls {
  display: flex;
  gap: 10px;
}

.week-info h3 {
  margin: 0;
  color: #1e293b;
}

.timetable-grid {
  background: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.grid-container {
  display: grid;
  grid-template-columns: 150px repeat(7, 1fr);
  min-width: 800px;
}

.grid-header {
  display: contents;
}

.time-header {
  background: #f8fafc;
  padding: 15px;
  font-weight: 600;
  color: #475569;
  border-bottom: 1px solid #e2e8f0;
  border-right: 1px solid #e2e8f0;
}

.day-header {
  background: #f8fafc;
  padding: 15px;
  text-align: center;
  border-bottom: 1px solid #e2e8f0;
  border-right: 1px solid #e2e8f0;
}

.day-header.today {
  background: #dbeafe;
  color: #1e40af;
}

.day-header.past {
  opacity: 0.6;
}

.day-name {
  font-weight: 600;
  margin-bottom: 4px;
}

.day-date {
  font-size: 12px;
  color: #64748b;
}

.time-row {
  display: contents;
}

.time-cell {
  background: #f8fafc;
  padding: 15px;
  border-bottom: 1px solid #e2e8f0;
  border-right: 1px solid #e2e8f0;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.period-name {
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 4px;
}

.time-range {
  font-size: 12px;
  color: #64748b;
  font-family: 'Monaco', 'Menlo', monospace;
}

.schedule-cell {
  min-height: 80px;
  padding: 8px;
  border-bottom: 1px solid #e2e8f0;
  border-right: 1px solid #e2e8f0;
  position: relative;
  transition: all 0.2s;
}

.schedule-cell.clickable:hover {
  background: #f8fafc;
  cursor: pointer;
}

.schedule-cell.has-schedule {
  padding: 0;
}

.schedule-cell.past {
  opacity: 0.7;
}

.schedule-content {
  padding: 8px;
  height: 100%;
  position: relative;
  color: white;
  border-radius: 4px;
}

.session-title {
  font-weight: 600;
  font-size: 13px;
  margin-bottom: 4px;
  line-height: 1.2;
}

.session-details {
  display: flex;
  gap: 8px;
  margin-bottom: 4px;
}

.session-type,
.lab-name {
  font-size: 11px;
  background: rgba(255, 255, 255, 0.2);
  padding: 2px 6px;
  border-radius: 10px;
}

.instructor-name,
.class-name {
  font-size: 11px;
  opacity: 0.9;
  margin-bottom: 2px;
}

.status-badge {
  position: absolute;
  top: 4px;
  right: 4px;
  font-size: 9px;
  padding: 2px 6px;
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.2);
  text-transform: uppercase;
  font-weight: 500;
}

.schedule-actions {
  position: absolute;
  bottom: 4px;
  right: 4px;
  display: flex;
  gap: 4px;
  opacity: 0;
  transition: opacity 0.2s;
}

.schedule-content:hover .schedule-actions {
  opacity: 1;
}

.action-btn {
  width: 20px;
  height: 20px;
  border: none;
  border-radius: 4px;
  background: rgba(255, 255, 255, 0.2);
  color: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 10px;
  transition: background 0.2s;
}

.action-btn:hover {
  background: rgba(255, 255, 255, 0.3);
}

.empty-cell-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #94a3b8;
  font-size: 12px;
  opacity: 0;
  transition: opacity 0.2s;
}

.schedule-cell.clickable:hover .empty-cell-content {
  opacity: 1;
}

.timetable-legend {
  margin-top: 25px;
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.timetable-legend h4 {
  margin: 0 0 10px 0;
  color: #1e293b;
  font-size: 14px;
}

.legend-items {
  display: flex;
  gap: 15px;
  margin-bottom: 20px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
}

.legend-color {
  width: 12px;
  height: 12px;
  border-radius: 2px;
}

.week-summary {
  margin-top: 20px;
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.summary-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 20px;
}

.stat-item {
  text-align: center;
}

.stat-number {
  display: block;
  font-size: 24px;
  font-weight: 600;
  color: #3b82f6;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 12px;
  color: #64748b;
  text-transform: uppercase;
  font-weight: 500;
}

/* Calendar View Styles */
.timetable-calendar-view {
  padding: 20px;
}

.month-navigation {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 25px;
  padding-bottom: 15px;
  border-bottom: 1px solid #e2e8f0;
}

.month-info h3 {
  margin: 0;
  color: #1e293b;
}

.calendar-grid {
  background: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  margin-bottom: 25px;
}

.calendar-header {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  background: #f8fafc;
}

.calendar-header .day-header {
  padding: 15px;
  text-align: center;
  font-weight: 600;
  color: #475569;
  border-right: 1px solid #e2e8f0;
}

.calendar-body {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
}

.calendar-date {
  min-height: 120px;
  padding: 8px;
  border-bottom: 1px solid #e2e8f0;
  border-right: 1px solid #e2e8f0;
  cursor: pointer;
  transition: background 0.2s;
}

.calendar-date:hover {
  background: #f8fafc;
}

.calendar-date.today {
  background: #dbeafe;
}

.calendar-date.selected {
  background: #3b82f6;
  color: white;
}

.calendar-date.other-month {
  opacity: 0.4;
}

.date-number {
  font-weight: 600;
  margin-bottom: 8px;
}

.date-schedules {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.schedule-item {
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 10px;
  color: white;
  cursor: pointer;
  transition: opacity 0.2s;
}

.schedule-item:hover {
  opacity: 0.8;
}

.schedule-time {
  font-weight: 500;
}

.schedule-title {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.schedule-lab {
  font-size: 9px;
  opacity: 0.8;
}

.more-schedules {
  font-size: 9px;
  color: #64748b;
  text-align: center;
  padding: 2px;
}

.selected-date-details {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

.selected-date-details h4 {
  margin: 0 0 15px 0;
  color: #1e293b;
}

.no-schedules {
  text-align: center;
  padding: 40px;
  color: #64748b;
}

.schedules-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.schedule-detail-item {
  background: #f8fafc;
  padding: 15px;
  border-radius: 6px;
  border-left: 4px solid;
}

.schedule-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.schedule-time {
  font-family: 'Monaco', 'Menlo', monospace;
  font-size: 13px;
  color: #64748b;
}

.schedule-period {
  font-size: 12px;
  color: #64748b;
}

.schedule-content h5 {
  margin: 0 0 8px 0;
  color: #1e293b;
}

.schedule-meta {
  display: flex;
  gap: 10px;
  margin-bottom: 8px;
}

.schedule-meta span {
  font-size: 12px;
  background: #e2e8f0;
  padding: 2px 8px;
  border-radius: 10px;
  color: #475569;
}

.schedule-description {
  font-size: 13px;
  color: #64748b;
  margin: 8px 0;
}

.schedule-actions {
  display: flex;
  gap: 8px;
  margin-top: 10px;
}

.month-summary {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* Schedule Modal Styles */
.schedule-modal {
  width: 90vw;
  max-width: 800px;
  max-height: 90vh;
  display: flex;
  flex-direction: column;
}

.schedule-modal .modal-header {
  flex-shrink: 0;
  padding: 20px 24px;
  border-bottom: 1px solid #e2e8f0;
  background: #f8fafc;
}

.schedule-modal .modal-body {
  flex: 1;
  overflow-y: auto;
  padding: 20px 24px;
}

.schedule-modal .modal-footer {
  flex-shrink: 0;
  padding: 16px 24px;
  border-top: 1px solid #e2e8f0;
  background: #f8fafc;
}

.form-section {
  margin-bottom: 25px;
  padding-bottom: 20px;
  border-bottom: 1px solid #e2e8f0;
}

.form-section:last-child {
  border-bottom: none;
}

.form-section h4 {
  margin: 0 0 15px 0;
  color: #1e293b;
  font-size: 16px;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 15px;
  margin-bottom: 15px;
}

.time-display {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px;
  background: #f0f9ff;
  border-radius: 6px;
  color: #1e40af;
  font-size: 14px;
  margin-top: 10px;
}

.color-input-group {
  display: flex;
  gap: 10px;
  align-items: center;
}

.color-input-group input[type="color"] {
  width: 40px;
  height: 40px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
}

.color-input-group input[type="text"] {
  flex: 1;
}

.schedule-preview {
  padding: 15px;
  border-radius: 6px;
  border-left: 4px solid;
}

.preview-title {
  font-weight: 600;
  margin-bottom: 5px;
  color: #1e293b;
}

.preview-type {
  font-size: 12px;
  text-transform: uppercase;
  color: #64748b;
  margin-bottom: 5px;
}

.preview-time {
  font-family: 'Monaco', 'Menlo', monospace;
  font-size: 12px;
  color: #64748b;
  margin-bottom: 5px;
}

.preview-location {
  font-size: 12px;
  color: #64748b;
}

/* Export Modal Styles */
.export-modal {
  width: 600px;
  max-width: 90vw;
}

.quick-export-buttons {
  display: flex;
  gap: 8px;
}

.export-options {
  margin-bottom: 25px;
}

.radio-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-top: 8px;
}

.radio-label {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  padding: 8px;
  border-radius: 4px;
  transition: background 0.2s;
}

.radio-label:hover {
  background: #f8fafc;
}

.radio-label input[type="radio"] {
  margin: 0;
}

.export-preview {
  background: #f8fafc;
  padding: 15px;
  border-radius: 6px;
  border-left: 4px solid #3b82f6;
}

.export-preview h4 {
  margin: 0 0 10px 0;
  color: #1e293b;
}

.preview-info p {
  margin: 5px 0;
  font-size: 14px;
  color: #64748b;
}

/* Status Colors */
.status-scheduled { background: #dcfce7; color: #166534; }
.status-completed { background: #f1f5f9; color: #475569; }
.status-cancelled { background: #fee2e2; color: #991b1b; }
.status-rescheduled { background: #fef3c7; color: #92400e; }
.status-migrated { background: #f3e8ff; color: #7c3aed; }

/* Responsive Design */
@media (max-width: 768px) {
  .comprehensive-timetable {
    padding: 15px;
  }

  .timetable-header {
    flex-direction: column;
    gap: 20px;
    align-items: stretch;
  }

  .header-actions {
    flex-direction: column;
    gap: 15px;
  }

  .admin-actions {
    flex-wrap: wrap;
  }

  .period-form .form-row {
    grid-template-columns: 1fr;
    gap: 10px;
  }

  .versions-table {
    font-size: 14px;
  }

  .versions-table th,
  .versions-table td {
    padding: 8px;
  }

  .grid-container {
    grid-template-columns: 120px repeat(7, 1fr);
    min-width: 600px;
  }

  .form-row {
    grid-template-columns: 1fr;
  }

  .summary-stats {
    grid-template-columns: repeat(2, 1fr);
  }

  .legend-items {
    flex-wrap: wrap;
  }

  .nav-controls {
    flex-direction: column;
    gap: 8px;
  }

  .week-navigation,
  .month-navigation {
    flex-direction: column;
    gap: 15px;
    text-align: center;
  }
}

/* Global Modal Fixes */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
  box-sizing: border-box;
}

.modal {
  background: white;
  border-radius: 12px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  display: flex;
  flex-direction: column;
  max-height: calc(100vh - 40px);
  width: 100%;
  max-width: 800px;
}

.modal-header {
  flex-shrink: 0;
  padding: 20px 24px;
  border-bottom: 1px solid #e2e8f0;
  background: #f8fafc;
  border-radius: 12px 12px 0 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.modal-body {
  flex: 1;
  overflow-y: auto;
  padding: 20px 24px;
}

.modal-footer {
  flex-shrink: 0;
  padding: 16px 24px;
  border-top: 1px solid #e2e8f0;
  background: #f8fafc;
  border-radius: 0 0 12px 12px;
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

/* Badge Positioning Fixes */
.badge-container {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 2rem;
}

.status-badge,
.role-badge,
.type-badge {
  display: inline-flex !important;
  align-items: center !important;
  justify-content: center !important;
  vertical-align: middle !important;
  white-space: nowrap !important;
  margin: 0 !important;
}

/* Table cell alignment for badges */
td .status-badge,
td .role-badge,
td .type-badge {
  display: inline-flex;
  position: relative;
}

/* Schedule Modal Readonly Fields */
.readonly-field {
  position: relative;
  display: flex;
  align-items: center;
}

.readonly-input {
  background-color: #f8fafc !important;
  border: 1px solid #e2e8f0 !important;
  color: #64748b !important;
  cursor: not-allowed !important;
  padding-right: 2.5rem !important;
  width: 100%;
  padding: 10px;
  border-radius: 6px;
  font-size: 14px;
}

.readonly-field i {
  position: absolute;
  right: 0.75rem;
  color: #94a3b8;
  pointer-events: none;
}

.readonly-field .readonly-input:focus {
  outline: none !important;
  box-shadow: none !important;
}

/* Weekly Class Schedule Modal */
.weekly-schedule-modal {
  width: 90vw;
  max-width: 900px;
  max-height: 90vh;
  display: flex;
  flex-direction: column;
}

.weekly-schedule-modal .modal-header h2 {
  display: flex;
  align-items: center;
  gap: 10px;
  color: #1e293b;
  font-size: 1.5rem;
}

.schedule-info {
  margin-bottom: 25px;
}

.info-card {
  background: #f0f9ff;
  border: 1px solid #bae6fd;
  border-radius: 8px;
  padding: 20px;
}

.info-card h4 {
  margin: 0 0 10px 0;
  color: #0c4a6e;
  font-size: 1.1rem;
}

.info-card p {
  margin: 5px 0;
  color: #0369a1;
  font-size: 0.9rem;
}

.form-section {
  margin-bottom: 25px;
  padding-bottom: 20px;
  border-bottom: 1px solid #e2e8f0;
}

.form-section:last-child {
  border-bottom: none;
}

.form-section h4 {
  margin: 0 0 15px 0;
  color: #1e293b;
  font-size: 1.1rem;
  font-weight: 600;
}

.form-section h5 {
  margin: 15px 0 10px 0;
  color: #475569;
  font-size: 1rem;
  font-weight: 500;
}

.period-info {
  margin-top: 15px;
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.info-badge {
  display: flex;
  align-items: center;
  gap: 8px;
  background: #f1f5f9;
  border: 1px solid #cbd5e1;
  border-radius: 6px;
  padding: 8px 12px;
  font-size: 0.9rem;
  color: #475569;
}

.info-badge i {
  color: #64748b;
}

.holiday-options {
  display: flex;
  flex-direction: column;
  gap: 10px;
  margin-bottom: 20px;
}

.custom-holidays {
  background: #fafafa;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 15px;
}

.add-holiday {
  display: flex;
  gap: 10px;
  align-items: center;
  margin-bottom: 15px;
}

.add-holiday input[type="date"] {
  flex: 1;
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 0.9rem;
}

.holiday-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.holiday-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  padding: 10px 12px;
  font-size: 0.9rem;
}

.holiday-item span {
  color: #374151;
}

.schedule-preview {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 20px;
  margin-top: 20px;
}

.schedule-preview h4 {
  margin: 0 0 15px 0;
  color: #1e293b;
  font-size: 1.1rem;
}

.preview-info {
  display: grid;
  gap: 10px;
}

.preview-item {
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 0.9rem;
}

.preview-item strong {
  min-width: 80px;
  color: #374151;
}

.preview-item span {
  color: #6b7280;
}
