.grading-modal {
  background: white;
  border-radius: 12px;
  width: 90%;
  max-width: 1000px;
  max-height: 90vh;
  display: flex;
  flex-direction: column;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  position: relative;
}

.grading-modal .modal-body {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
}

.grading-modal .modal-footer {
  flex-shrink: 0;
  padding: 16px 20px;
  border-top: 1px solid #e2e8f0;
  background: #f8fafc;
}

.grading-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid #e0e0e0;
  background: #f8f9fa;
  border-radius: 12px 12px 0 0;
}

.grading-modal-header h2 {
  margin: 0;
  color: #333;
  font-size: 1.5rem;
  font-weight: 600;
}

.close-btn {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #666;
  padding: 4px 8px;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.close-btn:hover {
  background: #e0e0e0;
  color: #333;
}

.grading-modal-content {
  padding: 24px;
  display: flex;
  flex-direction: column;
  gap: 24px;
}

/* Submission Info */
.submission-info {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

.info-section h3 {
  margin: 0 0 12px 0;
  color: #333;
  font-size: 1.1rem;
  font-weight: 600;
  border-bottom: 2px solid #007bff;
  padding-bottom: 4px;
}

.info-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 8px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.info-item label {
  font-weight: 600;
  color: #555;
  min-width: 100px;
}

.info-item span {
  color: #333;
  text-align: right;
}

.assignment-type {
  background: #e3f2fd;
  color: #1976d2;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 0.85rem;
  font-weight: 500;
}

/* Submitted Files */
.submitted-files h3 {
  margin: 0 0 16px 0;
  color: #333;
  font-size: 1.1rem;
  font-weight: 600;
  border-bottom: 2px solid #28a745;
  padding-bottom: 4px;
}

.files-grid {
  display: grid;
  gap: 12px;
}

.file-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  background: #f8f9fa;
  transition: all 0.2s ease;
}

.file-item:hover {
  border-color: #007bff;
  background: #f0f8ff;
}

.file-info {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
}

.file-icon {
  font-size: 24px;
}

.file-details {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.file-name {
  font-weight: 600;
  color: #333;
}

.file-type {
  font-size: 0.85rem;
  color: #666;
}

.file-actions {
  display: flex;
  gap: 8px;
}

/* Grading Section */
.grading-section h3 {
  margin: 0 0 16px 0;
  color: #333;
  font-size: 1.1rem;
  font-weight: 600;
  border-bottom: 2px solid #dc3545;
  padding-bottom: 4px;
}

.grade-inputs {
  display: flex;
  align-items: center;
  gap: 24px;
  margin-bottom: 16px;
}

.score-inputs {
  display: flex;
  gap: 16px;
}

.input-group {
  display: flex;
  flex-direction: column;
  gap: 4px;
  margin-bottom: 16px;
}

.input-group label {
  font-weight: 600;
  color: #555;
  font-size: 0.9rem;
}

.score-input,
.max-score-input,
.grade-letter-input {
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 1rem;
  width: 100px;
  transition: border-color 0.2s ease;
}

.score-input:focus,
.max-score-input:focus,
.grade-letter-input:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.grade-display {
  display: flex;
  align-items: center;
  justify-content: center;
}

.grade-circle {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: bold;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;
}

.grade-letter {
  font-size: 1.2rem;
  font-weight: 700;
}

.grade-percentage {
  font-size: 0.8rem;
  opacity: 0.9;
}

.feedback-textarea {
  padding: 12px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 1rem;
  font-family: inherit;
  resize: vertical;
  min-height: 100px;
  transition: border-color 0.2s ease;
}

.feedback-textarea:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

/* Modal Footer */
.grading-modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 20px 24px;
  border-top: 1px solid #e0e0e0;
  background: #f8f9fa;
  border-radius: 0 0 12px 12px;
}

/* Preview Modal */
.preview-modal {
  background: white;
  border-radius: 12px;
  width: 90%;
  max-width: 900px;
  max-height: 90vh;
  overflow: hidden;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  position: relative;
}

.preview-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #e0e0e0;
  background: #f8f9fa;
}

.preview-modal-header h3 {
  margin: 0;
  color: #333;
  font-size: 1.2rem;
  font-weight: 600;
}

.preview-modal-content {
  padding: 20px;
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
}

.unsupported-preview {
  text-align: center;
  color: #666;
}

.unsupported-preview p {
  margin-bottom: 16px;
  font-size: 1.1rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .grading-modal {
    width: 95%;
    margin: 20px auto;
  }
  
  .submission-info {
    grid-template-columns: 1fr;
  }
  
  .grade-inputs {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }
  
  .score-inputs {
    width: 100%;
  }
  
  .file-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }
  
  .file-actions {
    width: 100%;
    justify-content: flex-end;
  }
  
  .grading-modal-footer {
    flex-direction: column;
    gap: 8px;
  }
  
  .grading-modal-footer .btn {
    width: 100%;
  }
}

@media (max-width: 480px) {
  .grading-modal-content {
    padding: 16px;
  }
  
  .grading-modal-header,
  .grading-modal-footer {
    padding: 16px;
  }
  
  .info-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
  
  .info-item span {
    text-align: left;
  }
}
