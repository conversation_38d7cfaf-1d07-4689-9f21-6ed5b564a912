.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.grading-modal {
  background: white;
  border-radius: 12px;
  width: 90%;
  max-width: 900px;
  max-height: 90vh;
  display: flex;
  flex-direction: column;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.grading-modal .modal-body {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
}

.grading-modal .modal-footer {
  flex-shrink: 0;
  padding: 16px 20px;
  border-top: 1px solid #e2e8f0;
  background: #f8fafc;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid #e1e8ed;
  background: #f8f9fa;
  border-radius: 12px 12px 0 0;
}

.modal-header h3 {
  margin: 0;
  color: #2c3e50;
  font-size: 1.5rem;
  display: flex;
  align-items: center;
  gap: 8px;
}

.close-btn {
  background: none;
  border: none;
  font-size: 20px;
  cursor: pointer;
  color: #6c757d;
  padding: 8px;
  border-radius: 4px;
  transition: background-color 0.2s ease;
}

.close-btn:hover {
  background: #e9ecef;
}

.modal-body {
  padding: 25px;
}

.submission-info {
  margin-bottom: 25px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.submission-info h4 {
  margin: 0 0 15px 0;
  color: #2c3e50;
  font-size: 1.3rem;
}

.submission-meta {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 12px;
}

.meta-row {
  display: flex;
  align-items: center;
  gap: 8px;
}

.meta-row .label {
  font-weight: 600;
  color: #495057;
  min-width: 80px;
}

.meta-row .value {
  color: #6c757d;
}

.files-section {
  margin-bottom: 25px;
}

.files-section h5 {
  margin: 0 0 15px 0;
  color: #2c3e50;
  font-size: 1.1rem;
  display: flex;
  align-items: center;
  gap: 8px;
}

.files-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.file-item {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 15px;
}

.file-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.file-info .file-icon {
  font-size: 24px;
  margin-right: 12px;
}

.file-details {
  display: flex;
  flex-direction: column;
  flex: 1;
}

.file-details .filename {
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 4px;
}

.file-details .file-meta {
  color: #6c757d;
  font-size: 14px;
}

.download-btn {
  background: #007bff;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: background-color 0.2s ease;
  white-space: nowrap;
}

.download-btn:hover {
  background: #0056b3;
}

.no-files {
  text-align: center;
  padding: 30px;
  color: #6c757d;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px dashed #dee2e6;
}

.grading-section {
  background: #fff;
  border: 2px solid #007bff;
  border-radius: 12px;
  padding: 20px;
}

.grading-section h5 {
  margin: 0 0 20px 0;
  color: #007bff;
  font-size: 1.2rem;
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
}

.grade-inputs {
  display: flex;
  gap: 20px;
  align-items: flex-start;
  margin-bottom: 20px;
}

.score-inputs {
  display: flex;
  gap: 15px;
  flex: 1;
}

.input-group {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.input-group label {
  font-weight: 600;
  color: #495057;
  font-size: 14px;
}

.score-input,
.max-score-input {
  padding: 10px 12px;
  border: 2px solid #e9ecef;
  border-radius: 6px;
  font-size: 16px;
  font-weight: 600;
  width: 100px;
  transition: border-color 0.2s ease;
}

.score-input:focus,
.max-score-input:focus {
  outline: none;
  border-color: #007bff;
}

.grade-display {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  padding: 15px;
  background: linear-gradient(135deg, #007bff, #0056b3);
  border-radius: 12px;
  color: white;
  min-width: 120px;
}

.percentage {
  font-size: 2rem;
  font-weight: 700;
  line-height: 1;
}

.letter-grade {
  font-size: 1.5rem;
  font-weight: 600;
  opacity: 0.9;
}

.feedback-section {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.feedback-section label {
  font-weight: 600;
  color: #495057;
}

.feedback-textarea {
  padding: 12px;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  font-size: 14px;
  line-height: 1.5;
  resize: vertical;
  min-height: 100px;
  transition: border-color 0.2s ease;
}

.feedback-textarea:focus {
  outline: none;
  border-color: #007bff;
}

.feedback-textarea::placeholder {
  color: #adb5bd;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 20px;
  border-top: 1px solid #e1e8ed;
  background: #f8f9fa;
  border-radius: 0 0 12px 12px;
}

.btn {
  padding: 12px 24px;
  border-radius: 6px;
  border: none;
  cursor: pointer;
  font-weight: 600;
  font-size: 14px;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 6px;
}

.btn-secondary {
  background: #6c757d;
  color: white;
}

.btn-secondary:hover:not(:disabled) {
  background: #545b62;
}

.btn-primary {
  background: #007bff;
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background: #0056b3;
}

.btn:disabled {
  background: #adb5bd;
  cursor: not-allowed;
  opacity: 0.6;
}

/* Responsive Design */
@media (max-width: 768px) {
  .grading-modal {
    width: 95%;
    margin: 10px;
  }
  
  .submission-meta {
    grid-template-columns: 1fr;
  }
  
  .grade-inputs {
    flex-direction: column;
    gap: 15px;
  }
  
  .score-inputs {
    flex-direction: column;
  }
  
  .file-info {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }
  
  .download-btn {
    align-self: flex-start;
  }
  
  .modal-footer {
    flex-direction: column;
  }
}
