.student-grades {
  padding: 2rem;
  background: #f8fafc;
  min-height: 100vh;
}

.student-grades.loading {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 60vh;
}

.loading-spinner {
  font-size: 1.2rem;
  color: #667eea;
  text-align: center;
}

/* Page Header */
.page-header {
  margin-bottom: 2rem;
}

.page-header h1 {
  font-size: 2.5rem;
  font-weight: 700;
  color: #1a202c;
  margin: 0 0 0.5rem 0;
}

.page-header p {
  font-size: 1.1rem;
  color: #64748b;
  margin: 0;
}

/* Stats Section */
.stats-section {
  margin-bottom: 2rem;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
}

.stat-card {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 1rem;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.stat-card.primary { border-left: 4px solid #667eea; }
.stat-card.success { border-left: 4px solid #10b981; }
.stat-card.warning { border-left: 4px solid #f59e0b; }
.stat-card.info { border-left: 4px solid #3b82f6; }

.stat-icon {
  font-size: 2.5rem;
  opacity: 0.8;
}

.stat-content h3 {
  font-size: 2rem;
  font-weight: 700;
  margin: 0;
  color: #1a202c;
}

.stat-content p {
  font-size: 0.9rem;
  color: #64748b;
  margin: 0.25rem 0 0 0;
}

/* Controls Section */
.controls-section {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  margin-bottom: 2rem;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  display: flex;
  gap: 2rem;
  align-items: center;
}

.filter-controls,
.sort-controls {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.filter-controls label,
.sort-controls label {
  font-weight: 500;
  color: #374151;
}

.filter-controls select,
.sort-controls select {
  padding: 0.5rem 1rem;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  background: white;
  color: #374151;
  font-size: 0.9rem;
  cursor: pointer;
}

.filter-controls select:focus,
.sort-controls select:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* Grades Section */
.grades-section {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.grades-list {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.grade-card {
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  padding: 1.5rem;
  transition: all 0.2s ease;
}

.grade-card:hover {
  border-color: #667eea;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.grade-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1rem;
}

.assignment-info h3 {
  font-size: 1.3rem;
  font-weight: 600;
  color: #1a202c;
  margin: 0 0 0.75rem 0;
}

.assignment-meta {
  display: flex;
  gap: 1.5rem;
  font-size: 0.9rem;
  color: #64748b;
}

.grade-display {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.grade-circle {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 700;
}

.grade-letter {
  font-size: 1.5rem;
  line-height: 1;
}

.grade-percentage {
  font-size: 0.8rem;
  opacity: 0.9;
}

.grade-details {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.grade-score {
  font-size: 1.2rem;
  font-weight: 600;
  color: #1a202c;
}

.grade-date {
  font-size: 0.9rem;
  color: #64748b;
}

.pending-grade {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
}

.pending-indicator {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: #fef3c7;
  color: #d97706;
  padding: 0.75rem 1.5rem;
  border-radius: 20px;
  font-weight: 500;
}

.submitted-date {
  font-size: 0.9rem;
  color: #64748b;
}

.grade-feedback {
  background: #f8fafc;
  border-radius: 8px;
  padding: 1.5rem;
  border-top: 1px solid #e2e8f0;
  margin-top: 1rem;
}

.grade-feedback h4 {
  font-size: 1rem;
  font-weight: 600;
  color: #1a202c;
  margin: 0 0 0.75rem 0;
}

.grade-feedback p {
  color: #64748b;
  line-height: 1.6;
  margin: 0;
}

/* Empty State */
.empty-state {
  text-align: center;
  padding: 4rem 2rem;
  color: #64748b;
}

.empty-icon {
  font-size: 4rem;
  margin-bottom: 1rem;
  opacity: 0.6;
}

.empty-state h2 {
  font-size: 1.5rem;
  font-weight: 600;
  color: #1a202c;
  margin: 0 0 1rem 0;
}

.empty-state p {
  font-size: 1.1rem;
  line-height: 1.6;
  margin: 0;
  max-width: 500px;
  margin-left: auto;
  margin-right: auto;
}

/* Responsive Design */
@media (max-width: 768px) {
  .student-grades {
    padding: 1rem;
  }
  
  .page-header h1 {
    font-size: 2rem;
  }
  
  .stats-grid {
    grid-template-columns: 1fr;
  }
  
  .controls-section {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }
  
  .grade-header {
    flex-direction: column;
    gap: 1rem;
  }
  
  .assignment-meta {
    flex-direction: column;
    gap: 0.5rem;
  }
  
  .grade-display {
    align-self: center;
  }
  
  .empty-state {
    padding: 2rem 1rem;
  }
}
