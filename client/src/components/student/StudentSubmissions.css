.student-submissions {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.submissions-header {
  margin-bottom: 30px;
  padding: 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 20px;
}

.title-section {
  text-align: center;
  flex: 1;
}

.title-section h1 {
  margin: 0 0 10px 0;
  font-size: 2.5rem;
  font-weight: 700;
  color: white;
}

.title-section p {
  margin: 0;
  font-size: 1.1rem;
  opacity: 0.9;
  color: white;
}

.datetime-display {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  min-width: 200px;
}

.current-time {
  background: rgba(255, 255, 255, 0.15);
  padding: 12px 16px;
  border-radius: 8px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  text-align: center;
}

.time-label {
  display: block;
  font-size: 0.9rem;
  opacity: 0.8;
  margin-bottom: 4px;
}

.time-value {
  display: block;
  font-size: 1.1rem;
  font-weight: 600;
  font-family: 'Courier New', monospace;
}

.timezone-info {
  display: block;
  font-size: 0.8rem;
  opacity: 0.7;
  margin-top: 2px;
  font-style: italic;
}

@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    text-align: center;
  }

  .datetime-display {
    align-items: center;
  }

  .title-section h1 {
    font-size: 2rem;
  }
}

.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #3498db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Tab Navigation */
.tab-navigation {
  display: flex;
  gap: 1rem;
  margin-bottom: 2rem;
  border-bottom: 1px solid #e2e8f0;
}

.tab-button {
  background: none;
  border: none;
  padding: 1rem 1.5rem;
  font-size: 1rem;
  font-weight: 500;
  color: #64748b;
  cursor: pointer;
  border-bottom: 2px solid transparent;
  transition: all 0.2s ease;
}

.tab-button:hover {
  color: #667eea;
}

.tab-button.active {
  color: #667eea;
  border-bottom-color: #667eea;
}

/* Submission History */
.submissions-history {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.history-header {
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #e2e8f0;
}

.history-header h2 {
  font-size: 1.5rem;
  font-weight: 600;
  color: #1a202c;
  margin: 0 0 0.5rem 0;
}

.history-header p {
  color: #64748b;
  margin: 0;
}

.history-list {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.history-item {
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  padding: 1.5rem;
  transition: all 0.2s ease;
}

.history-item:hover {
  border-color: #667eea;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.history-item .history-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #f1f5f9;
}

.submission-info h3 {
  font-size: 1.2rem;
  font-weight: 600;
  color: #1a202c;
  margin: 0 0 0.5rem 0;
}

.assignment-title {
  font-size: 0.9rem;
  color: #64748b;
  margin: 0 0 0.75rem 0;
  font-style: italic;
}

.submission-meta {
  display: flex;
  gap: 1rem;
  font-size: 0.8rem;
  color: #64748b;
}

.status-badge {
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 500;
  text-transform: capitalize;
}

.status-badge.submitted {
  background: #dcfce7;
  color: #166534;
}

.status-badge.graded {
  background: #dbeafe;
  color: #1e40af;
}

.status-badge.pending {
  background: #fef3c7;
  color: #d97706;
}

.submission-files {
  background: #f8fafc;
  border-radius: 8px;
  padding: 1.5rem;
}

.submission-files h4 {
  font-size: 1rem;
  font-weight: 600;
  color: #1a202c;
  margin: 0 0 1rem 0;
}

.files-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.file-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 1rem;
}

.file-info {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.file-icon {
  font-size: 1.5rem;
}

.file-details {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.filename {
  font-weight: 500;
  color: #1a202c;
}

.file-meta {
  font-size: 0.8rem;
  color: #64748b;
  text-transform: capitalize;
}

.download-btn {
  background: #667eea;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  font-size: 0.9rem;
  cursor: pointer;
  transition: background-color 0.2s;
}

.download-btn:hover {
  background: #5a67d8;
}

.no-history {
  text-align: center;
  padding: 4rem 2rem;
  color: #64748b;
}

.no-history-icon {
  font-size: 4rem;
  margin-bottom: 1rem;
  opacity: 0.6;
}

.no-history h3 {
  font-size: 1.5rem;
  font-weight: 600;
  color: #1a202c;
  margin: 0 0 1rem 0;
}

.no-history p {
  font-size: 1.1rem;
  line-height: 1.6;
  margin: 0;
  max-width: 500px;
  margin-left: auto;
  margin-right: auto;
}

.assignments-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.assignment-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 20px;
  border: 1px solid #e1e8ed;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.assignment-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.assignment-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 15px;
}

.assignment-title-section {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  flex: 1;
}

.assignment-type-icon {
  font-size: 1.5rem;
}

.assignment-type-badge {
  background: #f1f5f9;
  color: #475569;
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 500;
  text-transform: capitalize;
}

.assignment-header h3 {
  color: #2c3e50;
  margin: 0;
  font-size: 18px;
  flex: 1;
  margin-right: 10px;
}

.assignment-status {
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  white-space: nowrap;
}

.assignment-status.active {
  background-color: #d4edda;
  color: #155724;
}

.assignment-status.completed {
  background-color: #cce5ff;
  color: #004085;
}

.assignment-status.pending {
  background-color: #fff3cd;
  color: #856404;
}

.assignment-details {
  margin-bottom: 20px;
}

.assignment-details .description {
  color: #5a6c7d;
  margin-bottom: 15px;
  line-height: 1.5;
}

.assignment-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  font-size: 14px;
  color: #7f8c8d;
}

.assignment-meta span {
  display: flex;
  align-items: center;
  gap: 5px;
  padding: 6px 10px;
  background: #f8f9fa;
  border-radius: 6px;
  font-size: 0.85rem;
}

.assignment-meta .scheduled-date {
  background: #e3f2fd;
  color: #1976d2;
  border: 1px solid #bbdefb;
}

.assignment-meta .due-date {
  background: #fff3e0;
  color: #f57c00;
  border: 1px solid #ffcc02;
}

.submission-section {
  border-top: 1px solid #e1e8ed;
  padding-top: 20px;
}

.submission-section h4 {
  color: #2c3e50;
  margin-bottom: 15px;
  font-size: 16px;
}

.file-upload-section {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.upload-item {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 15px;
  border: 1px solid #e9ecef;
}

.upload-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.upload-label {
  font-weight: 600;
  color: #495057;
}

.file-status {
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
}

.file-status.uploaded {
  background-color: #d4edda;
  color: #155724;
}

.file-status.pending {
  background-color: #fff3cd;
  color: #856404;
}

.uploaded-file {
  display: flex;
  align-items: center;
  gap: 10px;
  color: #495057;
}

.uploaded-file .filename {
  font-weight: 500;
}

.uploaded-file .filesize {
  color: #6c757d;
  font-size: 14px;
}

.upload-btn {
  background: #007bff;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: background-color 0.2s ease;
}

.upload-btn:hover {
  background: #0056b3;
}

.upload-btn:disabled {
  background: #6c757d;
  cursor: not-allowed;
}

.no-assignments {
  text-align: center;
  padding: 60px 20px;
  color: #6c757d;
}

.no-assignments h3 {
  color: #495057;
  margin-bottom: 10px;
}

/* Assignment PDF Section */
.assignment-pdf-section {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 20px;
}

.assignment-pdf-section h4 {
  margin: 0 0 12px 0;
  color: #495057;
  font-size: 16px;
  font-weight: 600;
}

.pdf-download {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: white;
  border: 1px solid #dee2e6;
  border-radius: 6px;
  padding: 12px;
}

.pdf-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.pdf-icon {
  font-size: 18px;
}

.pdf-name {
  font-weight: 500;
  color: #495057;
}

.pdf-download .download-btn {
  background: #007bff;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: background-color 0.2s ease;
}

.pdf-download .download-btn:hover {
  background: #0056b3;
}

/* Modal Styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal {
  background: white;
  border-radius: 12px;
  width: 90%;
  max-width: 500px;
  max-height: 90vh;
  display: flex;
  flex-direction: column;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.modal .modal-body {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
}

.modal .modal-footer {
  flex-shrink: 0;
  padding: 16px 20px;
  border-top: 1px solid #e2e8f0;
  background: #f8fafc;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid #e1e8ed;
}

.modal-header h3 {
  margin: 0;
  color: #2c3e50;
}

.close-btn {
  background: none;
  border: none;
  font-size: 20px;
  cursor: pointer;
  color: #6c757d;
  padding: 5px;
  border-radius: 4px;
  transition: background-color 0.2s ease;
}

.close-btn:hover {
  background: #f8f9fa;
}

.modal-body {
  padding: 20px;
}

.assignment-info {
  margin-bottom: 20px;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 8px;
}

.assignment-info h4 {
  margin: 0 0 10px 0;
  color: #2c3e50;
}

.assignment-info p {
  margin: 0;
  color: #6c757d;
}

.file-upload {
  margin-bottom: 20px;
}

.file-input-label {
  display: block;
  border: 2px dashed #dee2e6;
  border-radius: 8px;
  padding: 30px;
  text-align: center;
  cursor: pointer;
  transition: border-color 0.2s ease, background-color 0.2s ease;
}

.file-input-label:hover {
  border-color: #007bff;
  background-color: #f8f9ff;
}

.file-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10px;
  color: #6c757d;
}

.upload-icon {
  font-size: 24px;
}

.selected-file {
  display: flex;
  align-items: center;
  gap: 15px;
}

.file-icon {
  font-size: 24px;
}

.file-details {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.file-details .filename {
  font-weight: 600;
  color: #2c3e50;
}

.file-details .filesize {
  color: #6c757d;
  font-size: 14px;
}

.upload-requirements {
  background: #fff3cd;
  border: 1px solid #ffeaa7;
  border-radius: 8px;
  padding: 15px;
}

.upload-requirements h5 {
  margin: 0 0 10px 0;
  color: #856404;
}

.upload-requirements ul {
  margin: 0;
  padding-left: 20px;
  color: #856404;
}

.upload-requirements li {
  margin-bottom: 5px;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  padding: 20px;
  border-top: 1px solid #e1e8ed;
}

.btn {
  padding: 10px 20px;
  border-radius: 6px;
  border: none;
  cursor: pointer;
  font-weight: 500;
  transition: background-color 0.2s ease;
}

.btn-secondary {
  background: #6c757d;
  color: white;
}

.btn-secondary:hover {
  background: #545b62;
}

.btn-primary {
  background: #007bff;
  color: white;
}

.btn-primary:hover {
  background: #0056b3;
}

.btn:disabled {
  background: #6c757d;
  cursor: not-allowed;
  opacity: 0.6;
}

/* Responsive Design */
@media (max-width: 768px) {
  .assignments-grid {
    grid-template-columns: 1fr;
  }
  
  .assignment-meta {
    flex-direction: column;
    gap: 8px;
  }
  
  .modal {
    width: 95%;
    margin: 10px;
  }
  
  .file-upload-section {
    gap: 10px;
  }
  
  .upload-item {
    padding: 12px;
  }
}

/* Upcoming Assignment Styles */
.upcoming-badge {
  font-size: 0.75rem;
  color: #856404;
  background: #fff3cd;
  border: 1px solid #ffeaa7;
  border-radius: 12px;
  padding: 0.125rem 0.5rem;
  margin-left: 0.5rem;
  font-weight: 500;
}

.assignment-status.upcoming {
  background-color: #fff3cd;
  color: #856404;
  border: 1px solid #ffc107;
}

.upcoming-notice {
  background: #e7f3ff;
  border: 1px solid #b3d9ff;
  border-radius: 8px;
  padding: 1rem;
  margin-bottom: 1rem;
}

.upcoming-text {
  display: block;
  font-weight: 600;
  color: #0066cc;
  margin-bottom: 0.5rem;
}

.upcoming-description {
  display: block;
  font-size: 0.9rem;
  color: #4a5568;
}

.upcoming-restriction {
  background: #fff5f5;
  border: 1px solid #fed7d7;
  border-radius: 8px;
  padding: 1.5rem;
  text-align: center;
}

.restriction-notice {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
}

.restriction-icon {
  font-size: 3rem;
  color: #e53e3e;
}

.restriction-text {
  text-align: center;
}

.restriction-text p {
  margin: 0.5rem 0;
  color: #4a5568;
}

.restriction-text p:first-child {
  font-weight: 600;
  color: #e53e3e;
  font-size: 1.1rem;
}

@media (max-width: 768px) {
  .upcoming-badge {
    margin-left: 0;
    margin-top: 0.25rem;
  }

  .restriction-notice {
    gap: 0.75rem;
  }

  .restriction-icon {
    font-size: 2rem;
  }
}

/* New status-based styles */
.cancelled-restriction {
  background: #f8d7da;
  border-color: #f5c6cb;
}

.completed-submission {
  background: #d4edda;
  border: 1px solid #c3e6cb;
  border-radius: 8px;
  padding: 20px;
}

.completion-notice {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 15px;
  margin-bottom: 15px;
}

.completion-icon {
  font-size: 2rem;
}

.completion-text {
  text-align: left;
}

.completion-text p {
  margin: 5px 0;
  color: #155724;
}

.completion-text strong {
  color: #155724;
}

.cancelled-restriction .restriction-text p {
  color: #721c24;
}

.submitted-files {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.submitted-file {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 10px;
  background: #f8f9fa;
  border-radius: 6px;
  justify-content: space-between;
}

.file-label {
  font-weight: 600;
  color: #495057;
}

.file-name {
  color: #6c757d;
  font-family: monospace;
}

/* Status badges */
.status-completed {
  background: #28a745;
  color: white;
}

.status-in-progress {
  background: #ffc107;
  color: #212529;
}

.status-cancelled {
  background: #dc3545;
  color: white;
}

.status-upcoming {
  background: #17a2b8;
  color: white;
}

.group-badge {
  background: #6f42c1;
  color: white;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 500;
}

/* Submission controls */
.submit-assignment {
  margin-top: 20px;
  text-align: center;
}

.submit-btn {
  background: #28a745;
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 6px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.2s;
}

.submit-btn:hover {
  background: #218838;
}

.upload-disabled {
  color: #6c757d;
  font-style: italic;
}

/* Download button styles */
.download-btn {
  background: #007bff;
  color: white;
  border: none;
  padding: 6px 12px;
  border-radius: 4px;
  font-size: 0.875rem;
  cursor: pointer;
  transition: background-color 0.2s;
  display: flex;
  align-items: center;
  gap: 4px;
}

.download-btn:hover {
  background: #0056b3;
}

/* Tab navigation improvements */
.tab-navigation {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 2rem;
  border-bottom: 1px solid #e2e8f0;
  overflow-x: auto;
}

.tab-button {
  background: none;
  border: none;
  padding: 0.75rem 1rem;
  cursor: pointer;
  border-bottom: 2px solid transparent;
  transition: all 0.2s;
  white-space: nowrap;
  font-size: 0.875rem;
  color: #64748b;
}

.tab-button:hover {
  color: #334155;
  background: #f8fafc;
}

.tab-button.active {
  color: #3b82f6;
  border-bottom-color: #3b82f6;
  background: #eff6ff;
}
