.student-groups {
  padding: 2rem;
  background: #f8fafc;
  min-height: 100vh;
}

.student-groups.loading {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 60vh;
}

.loading-spinner {
  font-size: 1.2rem;
  color: #667eea;
  text-align: center;
}

/* Page Header */
.page-header {
  margin-bottom: 2rem;
}

.page-header h1 {
  font-size: 2.5rem;
  font-weight: 700;
  color: #1a202c;
  margin: 0 0 0.5rem 0;
}

.page-header p {
  font-size: 1.1rem;
  color: #64748b;
  margin: 0;
}

/* No Group Card */
.no-group-card {
  background: white;
  border-radius: 12px;
  padding: 3rem;
  text-align: center;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  max-width: 600px;
  margin: 0 auto;
}

.no-group-icon {
  font-size: 4rem;
  margin-bottom: 1rem;
  opacity: 0.6;
}

.no-group-card h2 {
  font-size: 1.8rem;
  font-weight: 600;
  color: #1a202c;
  margin: 0 0 1rem 0;
}

.no-group-card > p {
  font-size: 1.1rem;
  color: #64748b;
  margin: 0 0 2rem 0;
  line-height: 1.6;
}

.contact-info {
  background: #f1f5f9;
  border-radius: 8px;
  padding: 1.5rem;
  text-align: left;
}

.contact-info p {
  font-weight: 600;
  color: #1a202c;
  margin: 0 0 1rem 0;
}

.contact-info ul {
  margin: 0;
  padding-left: 1.5rem;
  color: #64748b;
}

.contact-info li {
  margin-bottom: 0.5rem;
}

/* Group Overview Card */
.group-overview-card {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  margin-bottom: 2rem;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.group-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1.5rem;
  padding-bottom: 1.5rem;
  border-bottom: 1px solid #e2e8f0;
}

.group-title-section h2 {
  font-size: 2rem;
  font-weight: 700;
  color: #1a202c;
  margin: 0 0 1rem 0;
}

.group-badges {
  display: flex;
  gap: 0.75rem;
  flex-wrap: wrap;
}

.class-badge {
  background: #e0e7ff;
  color: #3730a3;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: 500;
}

.leader-badge {
  background: #fef3c7;
  color: #d97706;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: 500;
}

.group-stats {
  display: flex;
  gap: 2rem;
}

.stat-item {
  text-align: center;
}

.stat-number {
  display: block;
  font-size: 2rem;
  font-weight: 700;
  color: #667eea;
}

.stat-label {
  font-size: 0.9rem;
  color: #64748b;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.group-description {
  background: #f8fafc;
  border-radius: 8px;
  padding: 1.5rem;
}

.group-description h3 {
  font-size: 1.2rem;
  font-weight: 600;
  color: #1a202c;
  margin: 0 0 0.75rem 0;
}

.group-description p {
  color: #64748b;
  line-height: 1.6;
  margin: 0;
}

/* Members Section */
.members-section {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  margin-bottom: 2rem;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #e2e8f0;
}

.section-header h2 {
  font-size: 1.5rem;
  font-weight: 600;
  color: #1a202c;
  margin: 0;
}

.member-legend {
  display: flex;
  gap: 1.5rem;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.9rem;
  color: #64748b;
}

.legend-icon {
  font-size: 1.1rem;
}

/* Members Grid */
.members-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1.5rem;
}

.member-card {
  background: #f8fafc;
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  padding: 1.5rem;
  position: relative;
  transition: all 0.2s ease;
}

.member-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.member-card.leader {
  border-color: #fbbf24;
  background: #fffbeb;
}

.member-avatar {
  font-size: 3rem;
  text-align: center;
  margin-bottom: 1rem;
}

.member-info h3 {
  font-size: 1.2rem;
  font-weight: 600;
  color: #1a202c;
  margin: 0 0 0.5rem 0;
  text-align: center;
}

.member-id {
  font-size: 0.9rem;
  color: #64748b;
  text-align: center;
  margin: 0 0 1rem 0;
}

.role-badge {
  display: block;
  text-align: center;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.role-badge.leader {
  background: #fef3c7;
  color: #d97706;
}

.role-badge.member {
  background: #e0e7ff;
  color: #3730a3;
}

.you-indicator {
  position: absolute;
  top: -8px;
  right: -8px;
  background: #10b981;
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 500;
}

/* Leader Info Card */
.leader-info-card {
  background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
  border-radius: 12px;
  padding: 2rem;
  margin-bottom: 2rem;
  border: 1px solid #fbbf24;
}

.leader-info-header {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.leader-icon {
  font-size: 2rem;
}

.leader-info-header h3 {
  font-size: 1.5rem;
  font-weight: 600;
  color: #92400e;
  margin: 0;
}

.responsibilities-list p {
  color: #92400e;
  font-weight: 500;
  margin: 0 0 1rem 0;
}

.responsibilities-list ul {
  color: #a16207;
  margin: 0;
  padding-left: 1.5rem;
}

.responsibilities-list li {
  margin-bottom: 0.5rem;
  line-height: 1.5;
}

/* Group Actions */
.group-actions {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.action-note p {
  color: #64748b;
  font-style: italic;
  margin: 0;
  line-height: 1.6;
}

.action-note strong {
  color: #1a202c;
}

/* Responsive Design */
@media (max-width: 768px) {
  .student-groups {
    padding: 1rem;
  }
  
  .page-header h1 {
    font-size: 2rem;
  }
  
  .group-header {
    flex-direction: column;
    gap: 1.5rem;
  }
  
  .group-stats {
    justify-content: center;
  }
  
  .section-header {
    flex-direction: column;
    gap: 1rem;
    align-items: flex-start;
  }
  
  .member-legend {
    align-self: stretch;
    justify-content: center;
  }
  
  .members-grid {
    grid-template-columns: 1fr;
  }
  
  .no-group-card {
    padding: 2rem 1.5rem;
  }
}
