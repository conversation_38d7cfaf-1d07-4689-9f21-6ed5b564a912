.assignment-submissions {
  padding: 20px;
  max-width: 1400px;
  margin: 0 auto;
}

.assignment-submissions-header {
  margin-bottom: 30px;
}

.assignment-submissions-header h1 {
  color: #2c3e50;
  margin-bottom: 8px;
  font-size: 2rem;
}

.assignment-submissions-header p {
  color: #7f8c8d;
  font-size: 1.1rem;
}

.assignment-submissions-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #3498db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.assignment-submissions-controls {
  display: flex;
  gap: 20px;
  margin-bottom: 25px;
  flex-wrap: wrap;
  align-items: center;
}

.search-section {
  flex: 1;
  min-width: 300px;
}

.search-input {
  width: 100%;
  padding: 12px 16px;
  border: 2px solid #e1e8ed;
  border-radius: 8px;
  font-size: 14px;
  transition: border-color 0.2s;
}

.search-input:focus {
  outline: none;
  border-color: #3498db;
}

.filter-section {
  display: flex;
  gap: 12px;
}

.filter-select {
  padding: 12px 16px;
  border: 2px solid #e1e8ed;
  border-radius: 8px;
  font-size: 14px;
  background: white;
  min-width: 140px;
  cursor: pointer;
}

.filter-select:focus {
  outline: none;
  border-color: #3498db;
}

.submissions-stats {
  display: flex;
  gap: 20px;
  margin-bottom: 25px;
  flex-wrap: wrap;
}

.stat-card {
  background: white;
  padding: 20px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  text-align: center;
  min-width: 120px;
  border: 1px solid #e1e8ed;
}

.stat-number {
  display: block;
  font-size: 2rem;
  font-weight: bold;
  color: #2c3e50;
  margin-bottom: 5px;
}

.stat-label {
  color: #7f8c8d;
  font-size: 0.9rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.assignment-submissions-table-container {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow-x: auto;
  overflow-y: hidden;
  border: 1px solid #e1e8ed;
}

.assignment-submissions-table {
  width: 100%;
  min-width: 1200px;
  border-collapse: collapse;
  font-size: 14px;
}

.assignment-submissions-table th {
  background: #f8f9fa;
  padding: 16px 12px;
  text-align: left;
  font-weight: 600;
  color: #2c3e50;
  border-bottom: 2px solid #e1e8ed;
  white-space: nowrap;
}

.assignment-submissions-table td {
  padding: 16px 12px;
  border-bottom: 1px solid #f1f3f4;
  vertical-align: top;
}

.submission-row:hover {
  background-color: #f8f9fa;
}

.assignment-cell {
  min-width: 200px;
}

.assignment-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.assignment-title {
  font-weight: 600;
  color: #2c3e50;
  line-height: 1.3;
}

.group-badge {
  background: #e3f2fd;
  color: #1976d2;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  width: fit-content;
}

.student-cell {
  min-width: 160px;
}

.student-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.student-name {
  font-weight: 500;
  color: #2c3e50;
}

.student-id {
  color: #7f8c8d;
  font-size: 12px;
}

.class-cell {
  min-width: 120px;
  font-weight: 500;
}

.type-cell {
  min-width: 100px;
}

.assignment-type-badge {
  background: #f3e5f5;
  color: #7b1fa2;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  text-transform: capitalize;
}

.deadline-cell, .submitted-cell {
  min-width: 140px;
  font-size: 13px;
  color: #5d6d7e;
}

.status-cell {
  min-width: 100px;
}

.status-badge {
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.status-completed {
  background: #d4edda;
  color: #155724;
}

.status-partial {
  background: #fff3cd;
  color: #856404;
}

.status-pending {
  background: #cce5ff;
  color: #004085;
}

.status-overdue {
  background: #f8d7da;
  color: #721c24;
}

.files-cell {
  min-width: 80px;
}

.grade-cell {
  min-width: 180px;
  position: relative;
}

.grade-display-container {
  display: flex;
  align-items: center;
  gap: 8px;
}

.grade-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
  flex: 1;
}

.grade-display {
  font-weight: 600;
  color: #2e7d32;
  font-size: 0.9rem;
}

.grade-score {
  font-size: 0.8rem;
  color: #666;
}

.grade-feedback {
  font-size: 0.9em;
  color: #007bff;
  cursor: help;
}

.no-grade {
  color: #999;
  font-style: italic;
  font-size: 0.85rem;
  flex: 1;
}

.edit-grade-btn {
  background: none;
  border: none;
  cursor: pointer;
  font-size: 1.1em;
  padding: 4px;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.edit-grade-btn:hover {
  background-color: #f0f0f0;
}

/* Inline grade editing styles */
.inline-grade-edit {
  display: flex;
  flex-direction: column;
  gap: 8px;
  padding: 8px;
  background-color: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #dee2e6;
}

.grade-inputs {
  display: flex;
  align-items: center;
  gap: 4px;
}

.score-input,
.max-score-input {
  width: 60px;
  padding: 4px 6px;
  border: 1px solid #ced4da;
  border-radius: 4px;
  font-size: 0.9em;
  text-align: center;
}

.score-input:focus,
.max-score-input:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.score-separator {
  font-weight: bold;
  color: #666;
}

.feedback-input {
  width: 100%;
  padding: 6px 8px;
  border: 1px solid #ced4da;
  border-radius: 4px;
  font-size: 0.85em;
  resize: vertical;
  min-height: 40px;
}

.feedback-input:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.grade-actions {
  display: flex;
  gap: 6px;
  justify-content: flex-end;
}

.save-grade-btn,
.cancel-grade-btn {
  background: none;
  border: none;
  cursor: pointer;
  font-size: 1.1em;
  padding: 4px 8px;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.save-grade-btn:hover {
  background-color: #d4edda;
}

.cancel-grade-btn:hover {
  background-color: #f8d7da;
}

.save-grade-btn:disabled,
.cancel-grade-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.file-indicators {
  display: flex;
  gap: 6px;
  align-items: center;
}

.file-indicator {
  font-size: 16px;
  padding: 4px;
  border-radius: 4px;
  background: #f8f9fa;
  border: 1px solid #e1e8ed;
}

.file-indicator.response {
  background: #e8f5e8;
  border-color: #4caf50;
}

.file-indicator.output {
  background: #fff3e0;
  border-color: #ff9800;
}

.no-files {
  color: #95a5a6;
  font-size: 12px;
  font-style: italic;
}

.actions-cell {
  min-width: 120px;
  position: sticky;
  right: 0;
  background: white;
  border-left: 1px solid #e1e8ed;
  z-index: 10;
}

.assignment-submissions-table th:last-child {
  position: sticky;
  right: 0;
  background: #f8f9fa;
  border-left: 1px solid #e1e8ed;
  z-index: 11;
}

.action-buttons {
  display: flex;
  gap: 6px;
  flex-wrap: wrap;
}

.action-btn {
  background: white;
  border: 1px solid #e1e8ed;
  border-radius: 6px;
  padding: 6px 8px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 32px;
  height: 32px;
}

.action-btn:hover {
  background: #f8f9fa;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.download-btn:hover {
  background: #e8f5e8;
  border-color: #4caf50;
}

.preview-btn:hover {
  background: #e3f2fd;
  border-color: #2196f3;
}



.no-submissions {
  text-align: center;
  padding: 60px 20px;
}

.no-data h3 {
  color: #2c3e50;
  margin-bottom: 10px;
}

.no-data p {
  color: #7f8c8d;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .assignment-submissions-table {
    font-size: 13px;
  }
  
  .assignment-submissions-table th,
  .assignment-submissions-table td {
    padding: 12px 8px;
  }
}

@media (max-width: 768px) {
  .assignment-submissions {
    padding: 15px;
  }
  
  .assignment-submissions-controls {
    flex-direction: column;
    gap: 15px;
  }
  
  .filter-section {
    width: 100%;
    justify-content: space-between;
  }
  
  .filter-select {
    flex: 1;
  }
  
  .submissions-stats {
    justify-content: center;
  }
  
  .assignment-submissions-table-container {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }

  .assignment-submissions-table {
    min-width: 1200px;
  }

  /* Ensure actions column stays visible on mobile */
  .actions-cell {
    box-shadow: -2px 0 4px rgba(0, 0, 0, 0.1);
  }
}

/* Preview Modal Styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
}

.preview-modal {
  background: white;
  border-radius: 12px;
  width: 90%;
  max-width: 1000px;
  max-height: 90vh;
  display: flex;
  flex-direction: column;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 25px;
  border-bottom: 1px solid #e0e0e0;
  background-color: #f8f9fa;
  border-radius: 12px 12px 0 0;
}

.modal-header h3 {
  margin: 0;
  color: #2c3e50;
  font-size: 1.3rem;
  font-weight: 600;
}

.modal-close-btn {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: #7f8c8d;
  padding: 5px 10px;
  border-radius: 6px;
  transition: all 0.2s ease;
}

.modal-close-btn:hover {
  background-color: #e74c3c;
  color: white;
}

.modal-body {
  padding: 0;
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.modal-body iframe {
  border-radius: 0 0 12px 12px;
}

.unsupported-preview {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
  color: #7f8c8d;
}

.unsupported-preview p {
  margin: 10px 0;
  font-size: 1.1rem;
}

.unsupported-preview .btn {
  margin-top: 20px;
  padding: 12px 24px;
  background-color: #3498db;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 1rem;
  transition: background-color 0.2s ease;
}

.unsupported-preview .btn:hover {
  background-color: #2980b9;
}

/* Responsive modal */
@media (max-width: 768px) {
  .modal-overlay {
    padding: 10px;
  }

  .preview-modal {
    width: 95%;
    max-height: 95vh;
  }

  .modal-header {
    padding: 15px 20px;
  }

  .modal-header h3 {
    font-size: 1.1rem;
  }

  .modal-body iframe {
    height: 500px !important;
  }
}
