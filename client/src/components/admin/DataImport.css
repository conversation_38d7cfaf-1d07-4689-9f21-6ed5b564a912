.data-import {
  padding: 2rem;
  max-width: 1200px;
  margin: 0 auto;
}

.page-header {
  text-align: center;
  margin-bottom: 2rem;
}

.page-header h1 {
  color: #1f2937;
  margin-bottom: 0.5rem;
}

.page-header p {
  color: #6b7280;
  font-size: 1.1rem;
}

.import-tabs {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 2rem;
  border-bottom: 1px solid #e5e7eb;
  overflow-x: auto;
}

.tab-button {
  background: none;
  border: none;
  padding: 1rem 1.5rem;
  cursor: pointer;
  border-bottom: 2px solid transparent;
  transition: all 0.2s;
  white-space: nowrap;
  font-size: 1rem;
  color: #6b7280;
}

.tab-button:hover {
  color: #374151;
  background: #f9fafb;
}

.tab-button.active {
  color: #3b82f6;
  border-bottom-color: #3b82f6;
  background: #eff6ff;
}

.import-content {
  margin-bottom: 3rem;
}

.import-section {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
}

.import-header {
  margin-bottom: 2rem;
}

.import-header h3 {
  color: #1f2937;
  margin-bottom: 0.5rem;
  font-size: 1.5rem;
}

.import-header p {
  color: #6b7280;
  font-size: 1rem;
  line-height: 1.6;
}

.import-actions {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  margin-bottom: 2rem;
}

.template-download,
.file-upload {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.btn-secondary {
  background: #6b7280;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-size: 1rem;
  cursor: pointer;
  transition: background-color 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.btn-secondary:hover {
  background: #4b5563;
}

.template-info {
  font-size: 0.875rem;
  color: #6b7280;
  font-style: italic;
}

.upload-label {
  cursor: pointer;
}

.file-input {
  display: none;
}

.upload-button {
  background: #3b82f6;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-size: 1rem;
  cursor: pointer;
  transition: background-color 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  border: 2px dashed transparent;
}

.upload-button:hover {
  background: #2563eb;
  border-color: #3b82f6;
}

.import-result {
  background: #f9fafb;
  border-radius: 8px;
  padding: 1.5rem;
  border: 1px solid #e5e7eb;
}

.result-summary h4 {
  color: #1f2937;
  margin-bottom: 1rem;
}

.result-stats {
  display: flex;
  gap: 1rem;
  margin-bottom: 1rem;
  flex-wrap: wrap;
}

.stat {
  padding: 0.5rem 1rem;
  border-radius: 6px;
  font-weight: 500;
  font-size: 0.875rem;
}

.stat.success {
  background: #d1fae5;
  color: #065f46;
}

.stat.error {
  background: #fee2e2;
  color: #991b1b;
}

.stat:not(.success):not(.error) {
  background: #e5e7eb;
  color: #374151;
}

.result-errors {
  margin-top: 1rem;
}

.result-errors h5 {
  color: #dc2626;
  margin-bottom: 0.5rem;
}

.result-errors ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.result-errors li {
  background: #fee2e2;
  color: #991b1b;
  padding: 0.5rem;
  margin-bottom: 0.25rem;
  border-radius: 4px;
  font-size: 0.875rem;
}

.import-instructions {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
}

.import-instructions h3 {
  color: #1f2937;
  margin-bottom: 1.5rem;
  text-align: center;
}

.instructions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
}

.instruction-card {
  background: #f9fafb;
  padding: 1.5rem;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
}

.instruction-card h4 {
  color: #1f2937;
  margin-bottom: 0.75rem;
  font-size: 1.1rem;
}

.instruction-card p {
  color: #6b7280;
  line-height: 1.6;
  margin: 0;
}

/* Responsive design */
@media (max-width: 768px) {
  .data-import {
    padding: 1rem;
  }

  .import-actions {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .result-stats {
    flex-direction: column;
    gap: 0.5rem;
  }

  .instructions-grid {
    grid-template-columns: 1fr;
  }
}

/* GitHub Deployment Section */
.deployment-section {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 0.75rem;
  padding: 1.5rem;
  margin-bottom: 2rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.deployment-section h3 {
  color: #1f2937;
  margin-bottom: 0.5rem;
  font-size: 1.25rem;
  font-weight: 600;
}

.deployment-section > p {
  color: #6b7280;
  margin-bottom: 1.5rem;
}

.git-status {
  background: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 0.5rem;
  padding: 1rem;
  margin-bottom: 1.5rem;
}

.status-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.75rem;
}

.branch-info {
  color: #374151;
  font-size: 0.9rem;
}

.last-commit {
  color: #6b7280;
  font-size: 0.85rem;
  margin-bottom: 0.75rem;
}

.changes-info {
  margin-top: 0.75rem;
}

.changes-badge {
  display: inline-block;
  background: #fef3c7;
  color: #92400e;
  padding: 0.25rem 0.75rem;
  border-radius: 0.375rem;
  font-size: 0.85rem;
  font-weight: 500;
  margin-bottom: 0.5rem;
}

.changes-list {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 0.375rem;
  padding: 0.75rem;
  max-height: 120px;
  overflow-y: auto;
}

.change-item {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 0.8rem;
  color: #374151;
  padding: 0.125rem 0;
}

.no-changes {
  margin-top: 0.75rem;
}

.status-badge.success {
  display: inline-block;
  background: #d1fae5;
  color: #065f46;
  padding: 0.25rem 0.75rem;
  border-radius: 0.375rem;
  font-size: 0.85rem;
  font-weight: 500;
}

.loading-status {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 1rem;
}

.deployment-actions {
  background: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 0.5rem;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
}

.commit-input {
  margin-bottom: 1rem;
}

.commit-input label {
  display: block;
  color: #374151;
  font-weight: 500;
  margin-bottom: 0.5rem;
}

.commit-message-input {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
  font-size: 0.9rem;
  transition: border-color 0.2s;
}

.commit-message-input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.commit-message-input:disabled {
  background: #f3f4f6;
  color: #9ca3af;
}

.push-buttons {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.btn-primary, .btn-secondary {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 0.375rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.btn-primary {
  background: #3b82f6;
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background: #2563eb;
}

.btn-secondary {
  background: #6b7280;
  color: white;
}

.btn-secondary:hover:not(:disabled) {
  background: #4b5563;
}

.btn-secondary.small {
  padding: 0.5rem 1rem;
  font-size: 0.85rem;
}

.btn-primary:disabled, .btn-secondary:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.push-result {
  border-radius: 0.5rem;
  padding: 1rem;
  margin-top: 1rem;
}

.push-result.success {
  background: #d1fae5;
  border: 1px solid #a7f3d0;
}

.push-result.error {
  background: #fee2e2;
  border: 1px solid #fca5a5;
}

.push-result h4 {
  margin: 0 0 0.5rem 0;
  color: #374151;
}

.push-result p {
  margin: 0 0 1rem 0;
  color: #6b7280;
}

.test-results, .push-output {
  margin-top: 1rem;
}

.test-results h5, .push-output h5 {
  margin: 0 0 0.5rem 0;
  color: #374151;
  font-size: 0.9rem;
}

.test-output, .git-output {
  background: #1f2937;
  color: #f9fafb;
  padding: 1rem;
  border-radius: 0.375rem;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 0.8rem;
  overflow-x: auto;
  white-space: pre-wrap;
  max-height: 200px;
  overflow-y: auto;
}

.test-output.success {
  border-left: 4px solid #10b981;
}

.test-output.error {
  border-left: 4px solid #ef4444;
}

/* Responsive adjustments for deployment section */
@media (max-width: 768px) {
  .status-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }

  .push-buttons {
    flex-direction: column;
  }

  .btn-primary, .btn-secondary {
    justify-content: center;
  }
}

/* Demo Students Section */
.demo-students-section {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 1.5rem;
  margin-bottom: 2rem;
}

.demo-students-section h3 {
  color: #1f2937;
  margin-bottom: 0.5rem;
  font-size: 1.25rem;
}

.demo-students-section p {
  color: #6b7280;
  margin-bottom: 1rem;
}

.demo-actions {
  display: flex;
  align-items: center;
  gap: 1rem;
  flex-wrap: wrap;
}

.demo-info {
  color: #6b7280;
  font-size: 0.9rem;
  flex: 1;
  min-width: 200px;
}

@media (max-width: 768px) {
  .demo-actions {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }

  .demo-info {
    min-width: auto;
  }
}
