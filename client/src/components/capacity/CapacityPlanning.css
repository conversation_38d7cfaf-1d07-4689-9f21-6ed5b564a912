.capacity-planning {
  padding: 2rem;
  max-width: 1400px;
  margin: 0 auto;
}

.capacity-header {
  margin-bottom: 2rem;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  flex-wrap: wrap;
  gap: 1rem;
}

.capacity-header .header-content {
  flex: 1;
}

.capacity-header h1 {
  color: #2c3e50;
  margin-bottom: 0.5rem;
  font-size: 2rem;
  font-weight: 600;
}

.capacity-header p {
  color: #7f8c8d;
  font-size: 1.1rem;
}

.header-actions {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.header-actions .btn {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 8px;
  font-size: 0.9rem;
  font-weight: 600;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.header-actions .btn-primary {
  background-color: #3498db;
  color: white;
}

.header-actions .btn-primary:hover {
  background-color: #2980b9;
}

.header-actions .btn-secondary {
  background-color: #95a5a6;
  color: white;
}

.header-actions .btn-secondary:hover {
  background-color: #7f8c8d;
}

.capacity-controls {
  display: flex;
  gap: 2rem;
  margin-bottom: 2rem;
  flex-wrap: wrap;
}

.control-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  min-width: 250px;
}

.control-group label {
  font-weight: 600;
  color: #2c3e50;
  font-size: 0.9rem;
}

.control-group select {
  padding: 0.75rem;
  border: 2px solid #e1e8ed;
  border-radius: 8px;
  font-size: 1rem;
  background-color: white;
  transition: border-color 0.2s ease;
}

.control-group select:focus {
  outline: none;
  border-color: #3498db;
  box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

.lab-info {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 2rem;
  border-radius: 12px;
  margin-bottom: 2rem;
}

.lab-info h2 {
  margin-bottom: 1rem;
  font-size: 1.5rem;
  font-weight: 600;
}

.lab-stats {
  display: flex;
  gap: 2rem;
  flex-wrap: wrap;
}

.stat {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.stat-label {
  font-size: 0.9rem;
  opacity: 0.9;
}

.stat-value {
  font-size: 1.2rem;
  font-weight: 600;
}

.capacity-tabs {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 2rem;
  border-bottom: 2px solid #e1e8ed;
}

.tab-button {
  padding: 1rem 2rem;
  border: none;
  background: none;
  font-size: 1rem;
  font-weight: 600;
  color: #7f8c8d;
  cursor: pointer;
  border-bottom: 3px solid transparent;
  transition: all 0.2s ease;
}

.tab-button:hover {
  color: #3498db;
}

.tab-button.active {
  color: #3498db;
  border-bottom-color: #3498db;
}

.seats-section {
  margin-bottom: 2rem;
}

.seats-section h3 {
  color: #2c3e50;
  margin-bottom: 1.5rem;
  font-size: 1.3rem;
  font-weight: 600;
}

.seats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 1rem;
  margin-bottom: 2rem;
}

.seat-card,
.computer-card {
  background: white;
  border: 2px solid #e1e8ed;
  border-radius: 12px;
  padding: 1.5rem;
  text-align: center;
  transition: all 0.2s ease;
  cursor: pointer;
}

.seat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* Color-coded seat statuses */
.seat-card.seat-available {
  border-color: #6c757d;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.seat-card.seat-available:hover {
  border-color: #007bff;
}

.seat-card.seat-reserved {
  border-color: #28a745;
  background: linear-gradient(135deg, #f8fff9 0%, #e8f5e8 100%);
}

.seat-card.seat-reserved:hover {
  border-color: #1e7e34;
}

.seat-card.seat-maintenance {
  border-color: #dc3545;
  background: linear-gradient(135deg, #fff5f5 0%, #ffe6e6 100%);
  cursor: not-allowed;
}

.seat-card.seat-maintenance:hover {
  border-color: #c82333;
  transform: none;
}

.seat-icon {
  font-size: 24px;
  margin-bottom: 8px;
  display: block;
}

/* Status-based seat icon colors */
.seat-available .seat-icon {
  filter: grayscale(100%);
}

.seat-reserved .seat-icon {
  filter: hue-rotate(120deg) saturate(1.5);
}

.seat-maintenance .seat-icon {
  filter: hue-rotate(0deg) saturate(1.5) brightness(0.8);
}

.seat-name {
  font-weight: 700;
  font-size: 1.1rem;
  color: #2c3e50;
  margin-bottom: 0.5rem;
}

.seat-number {
  font-size: 0.9rem;
  color: #7f8c8d;
  margin-bottom: 1rem;
}

.assignment-info {
  padding: 0.75rem;
  background: rgba(52, 152, 219, 0.1);
  border-radius: 8px;
}

.student-name {
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 0.25rem;
}

.student-id {
  font-size: 0.8rem;
  color: #7f8c8d;
}

.available-indicator {
  padding: 0.75rem;
  background: rgba(39, 174, 96, 0.1);
  border-radius: 8px;
  color: #27ae60;
  font-weight: 600;
  font-size: 0.9rem;
}

.status-indicator {
  font-weight: 600;
  font-size: 0.9rem;
  padding: 0.75rem;
  border-radius: 8px;
  margin-top: 8px;
}

.status-indicator.status-available {
  color: #6c757d;
  background-color: rgba(108, 117, 125, 0.1);
}

.status-indicator.status-reserved {
  color: #28a745;
  background-color: rgba(40, 167, 69, 0.1);
}

.status-indicator.status-maintenance {
  color: #dc3545;
  background-color: rgba(220, 53, 69, 0.1);
}

.computer-status {
  font-size: 0.8rem;
  font-weight: 500;
  margin-bottom: 0.5rem;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
}

.computer-status.functional {
  background-color: #d1fae5;
  color: #065f46;
}

.computer-status.non-functional {
  background-color: #fee2e2;
  color: #991b1b;
}

.computer-card.non-functional {
  opacity: 0.7;
  border-color: #fca5a5;
}

.computers-section {
  margin-bottom: 2rem;
}

.computers-section h3 {
  color: #2c3e50;
  margin-bottom: 1.5rem;
  font-size: 1.3rem;
  font-weight: 600;
}

.computers-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 1rem;
  margin-bottom: 2rem;
}

.computer-card {
  background: white;
  border: 2px solid #e1e8ed;
  border-radius: 12px;
  padding: 1.5rem;
  transition: all 0.2s ease;
}

.computer-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.computer-card.functional {
  border-color: #27ae60;
}

.computer-card.non-functional {
  border-color: #e74c3c;
  opacity: 0.6;
}

.computer-card.assigned {
  border-color: #3498db;
  background: linear-gradient(135deg, #f0f8ff 0%, #e1f0ff 100%);
}

.computer-name {
  font-weight: 700;
  font-size: 1.1rem;
  color: #2c3e50;
  margin-bottom: 0.5rem;
}

.computer-status {
  font-size: 0.8rem;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  margin-bottom: 1rem;
  display: inline-block;
}

.computer-status.functional {
  background: #d5f4e6;
  color: #27ae60;
}

.computer-status.non-functional {
  background: #ffeaea;
  color: #e74c3c;
}

.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400px;
  font-size: 1.2rem;
  color: #7f8c8d;
}

/* Computer Assignment Container */
.computer-assignment-container {
  margin-top: 1rem;
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.groups-table-section {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 1.5rem;
}

.groups-table-section h4 {
  margin: 0 0 1.5rem 0;
  color: #1f2937;
  font-size: 1.2rem;
  font-weight: 600;
}

.groups-table-container {
  overflow-x: auto;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
}

.groups-table {
  width: 100%;
  border-collapse: collapse;
  background: white;
}

.groups-table th {
  background: #f8f9fa;
  color: #374151;
  font-weight: 600;
  padding: 1rem;
  text-align: left;
  border-bottom: 2px solid #e5e7eb;
  font-size: 0.9rem;
}

.groups-table td {
  padding: 1rem;
  border-bottom: 1px solid #f3f4f6;
  vertical-align: top;
}

.group-row:hover {
  background: #f8f9fa;
}

.group-name-cell .group-name {
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 0.25rem;
}

.group-name-cell .group-description {
  font-size: 0.85rem;
  color: #6b7280;
  font-style: italic;
}

.members-cell {
  max-width: 300px;
}

.members-list {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.member-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.25rem 0;
}

.member-name {
  font-weight: 500;
  color: #374151;
}

.member-id {
  font-size: 0.8rem;
  color: #6b7280;
}

.leader-badge {
  background: #fbbf24;
  color: #92400e;
  padding: 0.125rem 0.5rem;
  border-radius: 12px;
  font-size: 0.7rem;
  font-weight: 600;
  text-transform: uppercase;
}

.member-count-cell {
  text-align: center;
}

.count-badge {
  background: #dbeafe;
  color: #1e40af;
  padding: 0.25rem 0.75rem;
  border-radius: 16px;
  font-weight: 600;
  font-size: 0.85rem;
}

.computer-assignment-cell {
  min-width: 150px;
}

.assigned-computer-info .computer-name {
  font-weight: 600;
  color: #059669;
  margin-bottom: 0.25rem;
}

.assigned-computer-info .assignment-status {
  font-size: 0.8rem;
  color: #065f46;
  background: #d1fae5;
  padding: 0.125rem 0.5rem;
  border-radius: 12px;
  display: inline-block;
}

.no-assignment-info .no-computer-text {
  color: #6b7280;
  font-style: italic;
  font-size: 0.9rem;
}

.actions-cell {
  min-width: 180px;
}

.computer-dropdown-container {
  display: flex;
  gap: 0.5rem;
  align-items: center;
}

.computer-dropdown {
  flex: 1;
  padding: 0.5rem;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  background: white;
  font-size: 0.9rem;
  cursor: pointer;
}

.computer-dropdown:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.no-computers-available {
  color: #ef4444;
  font-size: 0.85rem;
  font-style: italic;
}

.unassign-btn {
  background: #ef4444;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  font-size: 0.85rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

.unassign-btn:hover {
  background: #dc2626;
  transform: translateY(-1px);
}

/* Computer Status Summary */
.computers-summary-section {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 1.5rem;
}

.computers-summary-section h4 {
  margin: 0 0 1rem 0;
  color: #1f2937;
  font-size: 1.1rem;
  font-weight: 600;
}

.computers-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem 1rem;
  background: #f8f9fa;
  border-radius: 6px;
  border-left: 4px solid #e5e7eb;
}

.stat-label {
  font-weight: 500;
  color: #374151;
}

.stat-value {
  font-weight: 700;
  font-size: 1.1rem;
}

.stat-value.available {
  color: #059669;
}

.stat-value.assigned {
  color: #3b82f6;
}

.stat-value.maintenance {
  color: #ef4444;
}

/* Group Students Table */
.group-students-section {
  margin-top: 2rem;
  padding-top: 1.5rem;
  border-top: 1px solid #e5e7eb;
}

.group-students-section h4 {
  margin: 0 0 1rem 0;
  color: #1f2937;
  font-size: 1.1rem;
  font-weight: 600;
}

.students-table-container {
  overflow-x: auto;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
}

.students-table {
  width: 100%;
  border-collapse: collapse;
  background: white;
}

.students-table th {
  background: #f9fafb;
  color: #374151;
  font-weight: 600;
  padding: 0.75rem;
  text-align: left;
  border-bottom: 1px solid #e5e7eb;
  font-size: 0.875rem;
}

.students-table td {
  padding: 0.75rem;
  border-bottom: 1px solid #f3f4f6;
  color: #374151;
  font-size: 0.875rem;
}

.students-table tbody tr:hover {
  background: #f9fafb;
}

.students-table tbody tr:last-child td {
  border-bottom: none;
}

.member-badge {
  background: #e5e7eb;
  color: #374151;
  padding: 0.125rem 0.375rem;
  border-radius: 8px;
  font-size: 0.75rem;
  font-weight: 500;
}

/* Responsive Design for Computer Assignment Layout */
@media (max-width: 1024px) {
  .computer-assignment-layout {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
}

@media (max-width: 768px) {
  .capacity-controls {
    flex-direction: column;
  }
  
  .control-group {
    min-width: unset;
  }
  
  .lab-stats {
    flex-direction: column;
    gap: 1rem;
  }
  
  .groups-panel .group-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }
  
  .groups-panel .group-actions {
    align-self: stretch;
    justify-content: space-between;
  }
  
  .students-table-container {
    font-size: 0.8rem;
  }
  
  .students-table th,
  .students-table td {
    padding: 0.5rem;
  }
}

/* Assign Seat Button */
.assign-seat-btn {
  background: #3498db;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  font-size: 0.8rem;
  font-weight: 600;
  cursor: pointer;
  margin-top: 0.5rem;
  transition: all 0.2s ease;
  width: 100%;
}

.assign-seat-btn:hover {
  background: #2980b9;
  transform: translateY(-1px);
}

.assign-seat-btn:active {
  transform: translateY(0);
}

/* Unassign Button */
.unassign-btn {
  background: #e74c3c;
  color: white;
  border: none;
  padding: 0.4rem 0.8rem;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 600;
  cursor: pointer;
  margin-top: 0.5rem;
  transition: all 0.2s ease;
  width: 100%;
}

.unassign-btn:hover {
  background: #c0392b;
  transform: translateY(-1px);
}

/* Modal Overlay */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(2px);
}

.modal {
  background: white;
  border-radius: 12px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
  max-width: 600px;
  width: 90%;
  max-height: 80vh;
  display: flex;
  flex-direction: column;
  animation: modalSlideIn 0.3s ease-out;
}

.modal .modal-body {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
}

.modal .modal-footer {
  flex-shrink: 0;
  padding: 16px 20px;
  border-top: 1px solid #e2e8f0;
  background: #f8fafc;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  border-bottom: 1px solid #e1e8ed;
  background: #f8f9fa;
}

.modal-header h3 {
  margin: 0;
  color: #2c3e50;
  font-size: 1.25rem;
  font-weight: 600;
}

.close-btn {
  background: none;
  border: none;
  font-size: 1.5rem;
  color: #7f8c8d;
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 4px;
  transition: all 0.2s ease;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-btn:hover {
  background: #e9ecef;
  color: #495057;
}

.modal-body {
  padding: 1.5rem;
  max-height: 60vh;
  overflow-y: auto;
}

/* Student Selection */
.students-selection h4 {
  margin: 0 0 1rem 0;
  color: #2c3e50;
  font-size: 1.1rem;
  font-weight: 600;
}

.students-grid {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.student-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  border: 2px solid #e1e8ed;
  border-radius: 8px;
  background: white;
  cursor: pointer;
  transition: all 0.2s ease;
}

.student-option:hover {
  border-color: #3498db;
  background: #f8fbff;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(52, 152, 219, 0.1);
}

.student-info {
  flex: 1;
  text-align: left;
}

.student-info .student-name {
  font-weight: 600;
  color: #2c3e50;
  font-size: 1rem;
  margin-bottom: 0.25rem;
}

.student-info .student-id {
  font-size: 0.85rem;
  color: #7f8c8d;
  font-weight: 500;
}

.assign-btn {
  background: #27ae60;
  color: white;
  border: none;
  padding: 0.5rem 1.25rem;
  border-radius: 6px;
  font-size: 0.9rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  margin-left: 1rem;
}

.assign-btn:hover {
  background: #229954;
  transform: translateY(-1px);
}

.assign-btn:active {
  transform: translateY(0);
}

/* Empty state */
.no-students-message {
  text-align: center;
  padding: 2rem;
  color: #7f8c8d;
  font-style: italic;
}

.no-students-message p {
  margin: 0;
  font-size: 1rem;
}

.no-students {
  text-align: center;
  padding: 2rem;
  color: #7f8c8d;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.no-students p {
  margin: 0.5rem 0;
  font-size: 0.95rem;
}

/* Class-specific seat number */
.class-seat-number {
  font-size: 0.8rem;
  color: #3498db;
  font-weight: 600;
  background: #ecf0f1;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  margin-top: 0.25rem;
}

/* Lab Assignment Modal */
.large-modal {
  max-width: 900px;
  width: 90vw;
}

.lab-assignment-content {
  padding: 1rem 0;
}

.assignment-info {
  background: #f8f9fa;
  padding: 1rem;
  border-radius: 8px;
  margin-bottom: 1.5rem;
  border-left: 4px solid #3498db;
}

.assignment-info p {
  margin: 0.5rem 0;
  color: #2c3e50;
  font-size: 0.95rem;
}

.assignment-stats {
  display: flex;
  gap: 1.5rem;
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px solid #e1e8ed;
  flex-wrap: wrap;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.9rem;
  font-weight: 600;
  color: #2c3e50;
  background: white;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  border: 1px solid #e1e8ed;
}

.assignment-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 1.5rem;
}

.lab-assignment-card {
  border: 2px solid #e1e8ed;
  border-radius: 12px;
  padding: 1.5rem;
  background: white;
  transition: all 0.2s ease;
}

.lab-assignment-card:hover {
  border-color: #3498db;
  box-shadow: 0 4px 12px rgba(52, 152, 219, 0.1);
}

.lab-info {
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #e1e8ed;
}

.lab-info h4 {
  color: #2c3e50;
  margin: 0 0 0.5rem 0;
  font-size: 1.2rem;
  font-weight: 600;
}

.lab-info p {
  color: #7f8c8d;
  margin: 0 0 0.75rem 0;
  font-size: 0.9rem;
}

.capacity-badge {
  background: #3498db;
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
}

.assigned-classes h5 {
  color: #2c3e50;
  margin: 0 0 1rem 0;
  font-size: 1rem;
  font-weight: 600;
}

.class-list {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.assigned-class {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: #ecf0f1;
  padding: 0.5rem 0.75rem;
  border-radius: 6px;
  font-size: 0.9rem;
  color: #2c3e50;
}

.remove-btn {
  background: #e74c3c;
  color: white;
  border: none;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  font-size: 0.8rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s ease;
}

.remove-btn:hover {
  background: #c0392b;
}

.add-class select {
  width: 100%;
  padding: 0.5rem;
  border: 1px solid #e1e8ed;
  border-radius: 6px;
  font-size: 0.9rem;
  background: white;
}

.add-class select:focus {
  outline: none;
  border-color: #3498db;
}

.no-classes-available {
  padding: 0.75rem;
  background: #f8f9fa;
  border: 1px solid #e1e8ed;
  border-radius: 6px;
  text-align: center;
}

.no-classes-available small {
  color: #7f8c8d;
  font-style: italic;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  padding-top: 1rem;
  border-top: 1px solid #e1e8ed;
}

.modal-footer .btn {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 6px;
  font-size: 0.9rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

.modal-footer .btn-secondary {
  background: #95a5a6;
  color: white;
}

.modal-footer .btn-secondary:hover {
  background: #7f8c8d;
}

.modal-footer .btn-primary {
  background: #3498db;
  color: white;
}

.modal-footer .btn-primary:hover {
  background: #2980b9;
}

/* Unassigned Classes Section */
.unassigned-classes-section {
  margin-top: 2rem;
  padding: 1.5rem;
  background: #fff3cd;
  border: 1px solid #ffeaa7;
  border-radius: 8px;
}

.unassigned-classes-section h4 {
  color: #856404;
  margin: 0 0 0.5rem 0;
  font-size: 1.1rem;
  font-weight: 600;
}

.unassigned-classes-section p {
  color: #856404;
  margin: 0 0 1rem 0;
  font-size: 0.9rem;
}

.unassigned-classes-list {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 0.75rem;
}

.unassigned-class {
  background: white;
  padding: 0.75rem;
  border-radius: 6px;
  border: 1px solid #ffeaa7;
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.unassigned-class span {
  font-weight: 600;
  color: #2c3e50;
  font-size: 0.9rem;
}

.unassigned-class small {
  color: #7f8c8d;
  font-size: 0.8rem;
}

/* Class selection disabled state */
.control-group select:disabled {
  background-color: #f8f9fa;
  color: #6c757d;
  cursor: not-allowed;
}

.no-classes-warning {
  margin-top: 0.5rem;
}
