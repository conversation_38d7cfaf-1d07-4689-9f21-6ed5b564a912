/* LabSyncPro Logo Styles */
.labsyncpro-logo {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 12px;
}

/* <PERSON><PERSON> Variants */
.labsyncpro-logo.small {
  --logo-size: 32px;
  --text-size: 14px;
  gap: 6px;
}

.labsyncpro-logo.medium {
  --logo-size: 48px;
  --text-size: 18px;
  gap: 12px;
}

.labsyncpro-logo.large {
  --logo-size: 80px;
  --text-size: 24px;
  gap: 16px;
}

/* Logo Icon Container */
.logo-icon {
  position: relative;
  width: var(--logo-size);
  height: var(--logo-size);
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Ring Styles */
.logo-ring {
  position: absolute;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.outer-ring {
  width: 100%;
  height: 100%;
}

.inner-ring {
  width: 65%;
  height: 65%;
}

/* Ring Segments */
.ring-segment {
  position: absolute;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.outer-ring .ring-segment {
  width: 8px;
  height: 8px;
}

.inner-ring .ring-segment {
  width: 6px;
  height: 6px;
}

/* Segment Positions - Outer Ring */
.outer-ring .segment-1 {
  top: -4px;
  left: 50%;
  transform: translateX(-50%);
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.outer-ring .segment-2 {
  right: -4px;
  top: 50%;
  transform: translateY(-50%);
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.outer-ring .segment-3 {
  bottom: -4px;
  left: 50%;
  transform: translateX(-50%);
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.outer-ring .segment-4 {
  left: -4px;
  top: 50%;
  transform: translateY(-50%);
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

/* Segment Positions - Inner Ring */
.inner-ring .segment-1 {
  top: -3px;
  right: 20%;
  background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
}

.inner-ring .segment-2 {
  bottom: -3px;
  left: 50%;
  transform: translateX(-50%);
  background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
}

.inner-ring .segment-3 {
  left: -3px;
  top: 30%;
  background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
}

/* Center Core */
.logo-core {
  position: relative;
  width: 30%;
  height: 30%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.core-dot {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  box-shadow: 0 0 10px rgba(102, 126, 234, 0.5);
}

.core-pulse {
  position: absolute;
  width: 150%;
  height: 150%;
  border-radius: 50%;
  background: radial-gradient(circle, rgba(102, 126, 234, 0.3) 0%, transparent 70%);
}

/* Connection Lines */
.connection-lines {
  position: absolute;
  width: 100%;
  height: 100%;
}

.line {
  position: absolute;
  background: linear-gradient(90deg, rgba(102, 126, 234, 0.6), transparent);
  border-radius: 1px;
}

.line-1 {
  width: 20%;
  height: 1px;
  top: 50%;
  left: 15%;
  transform: translateY(-50%) rotate(45deg);
}

.line-2 {
  width: 20%;
  height: 1px;
  top: 50%;
  right: 15%;
  transform: translateY(-50%) rotate(-45deg);
}

.line-3 {
  width: 20%;
  height: 1px;
  bottom: 25%;
  left: 50%;
  transform: translateX(-50%) rotate(135deg);
}

.line-4 {
  width: 20%;
  height: 1px;
  top: 25%;
  left: 50%;
  transform: translateX(-50%) rotate(-135deg);
}

/* Logo Text */
.logo-text {
  display: flex;
  align-items: center;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
  font-weight: 700;
  font-size: var(--text-size);
  letter-spacing: -0.02em;
}

.logo-lab {
  color: #667eea;
}

.logo-sync {
  color: #764ba2;
  margin: 0 1px;
}

.logo-pro {
  color: #f093fb;
}

/* Animations */
.labsyncpro-logo.animated .outer-ring {
  animation: rotate-clockwise 8s linear infinite;
}

.labsyncpro-logo.animated .inner-ring {
  animation: rotate-counter-clockwise 6s linear infinite;
}

.labsyncpro-logo.animated .core-pulse {
  animation: pulse-glow 2s ease-in-out infinite;
}

.labsyncpro-logo.animated .connection-lines {
  animation: fade-lines 3s ease-in-out infinite;
}

.labsyncpro-logo.animated .logo-text {
  animation: text-glow 4s ease-in-out infinite;
}

/* Keyframes */
@keyframes rotate-clockwise {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes rotate-counter-clockwise {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(-360deg);
  }
}

@keyframes pulse-glow {
  0%, 100% {
    opacity: 0.6;
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(1.1);
  }
}

@keyframes fade-lines {
  0%, 100% {
    opacity: 0.3;
  }
  50% {
    opacity: 0.8;
  }
}

@keyframes text-glow {
  0%, 100% {
    text-shadow: 0 0 5px rgba(102, 126, 234, 0.3);
  }
  50% {
    text-shadow: 0 0 15px rgba(102, 126, 234, 0.6);
  }
}

/* Hover Effects */
.labsyncpro-logo:hover .core-dot {
  box-shadow: 0 0 20px rgba(102, 126, 234, 0.8);
  transform: scale(1.1);
  transition: all 0.3s ease;
}

.labsyncpro-logo:hover .logo-text {
  transform: scale(1.05);
  transition: all 0.3s ease;
}
