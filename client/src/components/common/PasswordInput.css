.password-input-container {
  position: relative;
  display: flex;
  align-items: center;
}

.password-input {
  width: 100%;
  padding: 12px 45px 12px 12px;
  border: 2px solid #ecf0f1;
  border-radius: 8px;
  font-size: 16px;
  transition: border-color 0.3s ease;
  background: white;
}

.password-input:focus {
  outline: none;
  border-color: #3498db;
}

.password-input:disabled {
  background-color: #f8f9fa;
  color: #6c757d;
  cursor: not-allowed;
}

.password-toggle-btn {
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  cursor: pointer;
  padding: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #6c757d;
  transition: color 0.2s ease;
  z-index: 1;
}

.password-toggle-btn:hover {
  color: #495057;
}

.password-toggle-btn:disabled {
  cursor: not-allowed;
  opacity: 0.5;
}

.password-toggle-btn svg {
  width: 20px;
  height: 20px;
}

/* Ensure the input doesn't have conflicting styles */
.password-input-container .password-input {
  padding-right: 45px;
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .password-input {
    background: #2c3e50;
    border-color: #34495e;
    color: #ecf0f1;
  }
  
  .password-input:focus {
    border-color: #3498db;
  }
  
  .password-toggle-btn {
    color: #bdc3c7;
  }
  
  .password-toggle-btn:hover {
    color: #ecf0f1;
  }
}
