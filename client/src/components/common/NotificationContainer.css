/* Notification Container */
.notification-container {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 10000;
  display: flex;
  flex-direction: column;
  gap: 12px;
  max-width: 400px;
  width: 100%;
}

/* Individual Notification */
.notification {
  background: white;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  border: 1px solid #e5e7eb;
  overflow: hidden;
  position: relative;
  transform: translateX(100%);
  animation: slideIn 0.3s ease-out forwards;
}

.notification:hover {
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

/* Notification Types */
.notification-success {
  border-left: 4px solid #10b981;
}

.notification-error {
  border-left: 4px solid #ef4444;
}

.notification-warning {
  border-left: 4px solid #f59e0b;
}

.notification-info {
  border-left: 4px solid #3b82f6;
}

/* Notification Content */
.notification-content {
  padding: 16px;
}

.notification-header {
  display: flex;
  align-items: flex-start;
  gap: 12px;
}

.notification-icon {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 14px;
  color: white;
  flex-shrink: 0;
}

.notification-success .notification-icon {
  background: #10b981;
}

.notification-error .notification-icon {
  background: #ef4444;
}

.notification-warning .notification-icon {
  background: #f59e0b;
}

.notification-info .notification-icon {
  background: #3b82f6;
}

.notification-text {
  flex: 1;
  min-width: 0;
}

.notification-title {
  font-weight: 600;
  font-size: 14px;
  color: #111827;
  margin-bottom: 4px;
  line-height: 1.4;
}

.notification-message {
  font-size: 13px;
  color: #6b7280;
  line-height: 1.4;
  word-wrap: break-word;
}

.notification-close {
  background: none;
  border: none;
  font-size: 20px;
  color: #9ca3af;
  cursor: pointer;
  padding: 0;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  transition: all 0.2s ease;
  flex-shrink: 0;
}

.notification-close:hover {
  background: #f3f4f6;
  color: #374151;
}

/* Notification Action */
.notification-action {
  margin-top: 12px;
  padding-top: 12px;
  border-top: 1px solid #f3f4f6;
}

.notification-action-btn {
  background: #3b82f6;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 13px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.notification-action-btn:hover {
  background: #2563eb;
  transform: translateY(-1px);
}

/* Progress Bar */
.notification-progress {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: #f3f4f6;
  overflow: hidden;
}

.notification-progress-bar {
  height: 100%;
  width: 100%;
  transform: translateX(-100%);
  animation: progress linear forwards;
}

.notification-success .notification-progress-bar {
  background: #10b981;
}

.notification-error .notification-progress-bar {
  background: #ef4444;
}

.notification-warning .notification-progress-bar {
  background: #f59e0b;
}

.notification-info .notification-progress-bar {
  background: #3b82f6;
}

/* Animations */
@keyframes slideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes progress {
  from {
    transform: translateX(-100%);
  }
  to {
    transform: translateX(0);
  }
}

/* Responsive Design */
@media (max-width: 640px) {
  .notification-container {
    top: 10px;
    right: 10px;
    left: 10px;
    max-width: none;
  }
  
  .notification {
    margin: 0;
  }
  
  .notification-content {
    padding: 14px;
  }
  
  .notification-title {
    font-size: 13px;
  }
  
  .notification-message {
    font-size: 12px;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .notification {
    background: #1f2937;
    border-color: #374151;
    color: #f9fafb;
  }
  
  .notification-title {
    color: #f9fafb;
  }
  
  .notification-message {
    color: #d1d5db;
  }
  
  .notification-close {
    color: #9ca3af;
  }
  
  .notification-close:hover {
    background: #374151;
    color: #f3f4f6;
  }
  
  .notification-action {
    border-top-color: #374151;
  }
  
  .notification-progress {
    background: #374151;
  }
}
