.grades {
  padding: 2rem;
  width: 100%;
  margin: 0;
}

.grades-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  flex-wrap: wrap;
  gap: 1rem;
}

.grades-header .header-content {
  text-align: left;
}

.grades-header .header-actions {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.grades-header h1 {
  color: #2c3e50;
  margin-bottom: 0.5rem;
  font-size: 2.5rem;
  font-weight: 600;
}

.grades-header p {
  color: #7f8c8d;
  font-size: 1.1rem;
}

.grades-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  color: #7f8c8d;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #ecf0f1;
  border-top: 4px solid #3498db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.grades-controls {
  background: white;
  padding: 1.5rem;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  margin-bottom: 2rem;
}

.search-section {
  margin-bottom: 1.5rem;
}

.search-input {
  width: 100%;
  padding: 0.75rem 1rem;
  border: 2px solid #ecf0f1;
  border-radius: 8px;
  font-size: 1rem;
  transition: border-color 0.3s ease;
}

.search-input:focus {
  outline: none;
  border-color: #3498db;
}

.filter-tabs {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.filter-tab {
  padding: 0.75rem 1.5rem;
  border: 2px solid #ecf0f1;
  background: white;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 500;
  color: #7f8c8d;
}

.filter-tab:hover {
  border-color: #3498db;
  color: #3498db;
}

.filter-tab.active {
  background: #3498db;
  border-color: #3498db;
  color: white;
}

/* Table Styles */
.grades-table-container {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  overflow-x: auto;
}

.grades-table {
  width: 100%;
  border-collapse: collapse;
  min-width: 1200px;
}

.grades-table th {
  background: #f8f9fa;
  color: #2c3e50;
  font-weight: 600;
  padding: 1rem 0.75rem;
  text-align: left;
  border-bottom: 2px solid #ecf0f1;
  font-size: 0.9rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  position: sticky;
  top: 0;
  z-index: 10;
}

.grades-table td {
  padding: 1rem 0.75rem;
  border-bottom: 1px solid #ecf0f1;
  vertical-align: top;
}

.grade-row:hover {
  background: #f8f9fa;
}

/* Student Cell */
.student-cell {
  min-width: 150px;
}

.student-name {
  color: #2c3e50;
  font-weight: 600;
  font-size: 0.95rem;
  margin-bottom: 0.25rem;
}

.student-id {
  color: #7f8c8d;
  font-size: 0.85rem;
  font-weight: 500;
}

/* Assignment Cell */
.assignment-cell {
  min-width: 200px;
}

.assignment-title {
  color: #2c3e50;
  font-weight: 600;
  font-size: 0.95rem;
  margin-bottom: 0.25rem;
  line-height: 1.3;
}

.file-count {
  color: #7f8c8d;
  font-size: 0.8rem;
}

/* Class Cell */
.class-cell {
  min-width: 120px;
}

.group-name {
  color: #7f8c8d;
  font-size: 0.85rem;
  margin-top: 0.25rem;
}

/* Status Cell */
.status-cell {
  min-width: 100px;
}

.status-badge {
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  color: white;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  display: inline-block;
}

/* Score Cell */
.score-cell {
  min-width: 100px;
}

.score-display .score {
  font-size: 1.1rem;
  font-weight: 700;
  display: block;
  margin-bottom: 0.25rem;
}

.score-display .percentage {
  color: #7f8c8d;
  font-size: 0.8rem;
  font-weight: 500;
}

.no-score, .no-grade, .no-feedback, .no-grader {
  color: #bdc3c7;
  font-style: italic;
}

/* Grade Cell */
.grade-cell {
  min-width: 80px;
}

.grade-letter {
  font-size: 1.1rem;
  font-weight: 700;
  color: #2c3e50;
}

/* Feedback Cell */
.feedback-cell {
  min-width: 200px;
  max-width: 250px;
}

.feedback-preview {
  color: #5a6c7d;
  font-size: 0.9rem;
  line-height: 1.4;
  cursor: help;
}

/* Graded By Cell */
.graded-by-cell {
  min-width: 120px;
}

.grader-name {
  color: #2c3e50;
  font-weight: 500;
  font-size: 0.9rem;
  margin-bottom: 0.25rem;
}

.graded-date {
  color: #7f8c8d;
  font-size: 0.8rem;
}

/* Actions Cell */
.actions-cell {
  min-width: 80px;
  position: sticky;
  right: 0;
  background: white;
  border-left: 1px solid #ecf0f1;
}

.grade-row:hover .actions-cell {
  background: #f8f9fa;
}

.btn-sm {
  padding: 0.5rem 0.75rem;
  font-size: 0.8rem;
  border-radius: 6px;
  min-width: auto;
}

.btn {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.3s ease;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  font-size: 0.9rem;
}

.btn-outline {
  background: transparent;
  border: 2px solid #3498db;
  color: #3498db;
}

.btn-outline:hover {
  background: #3498db;
  color: white;
}

.no-grades {
  text-align: center;
  padding: 3rem;
  color: #7f8c8d;
}

.no-grades h3 {
  color: #2c3e50;
  margin-bottom: 1rem;
  font-size: 1.5rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .grades {
    padding: 1rem;
  }

  .grades-header h1 {
    font-size: 2rem;
  }

  .grades-header {
    flex-direction: column;
    align-items: flex-start;
  }

  .grades-header .header-actions {
    width: 100%;
    justify-content: flex-start;
  }

  .filter-tabs {
    flex-direction: column;
  }

  .filter-tab {
    text-align: center;
  }

  .grades-table {
    min-width: 800px;
  }

  .grades-table th,
  .grades-table td {
    padding: 0.75rem 0.5rem;
  }

  .student-cell,
  .assignment-cell,
  .feedback-cell {
    min-width: 120px;
  }
}

@media (max-width: 480px) {
  .grades {
    padding: 0.5rem;
  }

  .grades-controls {
    padding: 1rem;
  }

  .grades-header .header-actions {
    flex-direction: column;
    gap: 0.5rem;
  }

  .grades-table {
    min-width: 600px;
  }

  .grades-table th,
  .grades-table td {
    padding: 0.5rem 0.25rem;
    font-size: 0.85rem;
  }

  .student-name,
  .assignment-title {
    font-size: 0.85rem;
  }

  .student-id,
  .file-count,
  .graded-date {
    font-size: 0.75rem;
  }
}

/* Modal Styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 1rem;
}

.grading-modal {
  background: white;
  border-radius: 12px;
  width: 100%;
  max-width: 800px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  border-bottom: 1px solid #ecf0f1;
}

.modal-header h2 {
  margin: 0;
  color: #2c3e50;
  font-size: 1.5rem;
}

.close-btn {
  background: none;
  border: none;
  font-size: 2rem;
  color: #7f8c8d;
  cursor: pointer;
  padding: 0;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.close-btn:hover {
  background: #ecf0f1;
  color: #2c3e50;
}

.modal-content {
  padding: 1.5rem;
}

.submission-info {
  margin-bottom: 2rem;
}

.submission-info h3 {
  color: #2c3e50;
  margin-bottom: 1rem;
  font-size: 1.3rem;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
}

.info-item {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.info-item label {
  font-weight: 600;
  color: #7f8c8d;
  font-size: 0.9rem;
}

.info-item span {
  color: #2c3e50;
  font-weight: 500;
}

.submission-files {
  margin-bottom: 2rem;
}

.submission-files h4 {
  color: #2c3e50;
  margin-bottom: 1rem;
}

.file-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.file-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #ecf0f1;
}

.file-name {
  color: #2c3e50;
  font-weight: 500;
}

.btn-sm {
  padding: 0.5rem 1rem;
  font-size: 0.8rem;
}

.submission-text {
  margin-bottom: 2rem;
}

.submission-text h4 {
  color: #2c3e50;
  margin-bottom: 1rem;
}

.text-content {
  background: #f8f9fa;
  padding: 1rem;
  border-radius: 8px;
  border: 1px solid #ecf0f1;
  color: #2c3e50;
  line-height: 1.6;
  white-space: pre-wrap;
}

.grading-section {
  background: #f1f2f6;
  padding: 1.5rem;
  border-radius: 8px;
  margin-bottom: 1rem;
}

.score-input {
  margin-bottom: 1.5rem;
}

.score-input label {
  display: block;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 0.5rem;
}

.score-controls {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.score-field {
  width: 100px;
  padding: 0.75rem;
  border: 2px solid #ecf0f1;
  border-radius: 8px;
  font-size: 1.1rem;
  font-weight: 600;
  text-align: center;
}

.score-field:focus {
  outline: none;
  border-color: #3498db;
}

.max-score {
  color: #7f8c8d;
  font-weight: 600;
  font-size: 1.1rem;
}

.score-percentage {
  font-weight: 700;
  font-size: 1.2rem;
}

.feedback-input label {
  display: block;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 0.5rem;
}

.feedback-field {
  width: 100%;
  padding: 0.75rem;
  border: 2px solid #ecf0f1;
  border-radius: 8px;
  font-family: inherit;
  font-size: 1rem;
  line-height: 1.5;
  resize: vertical;
  min-height: 120px;
}

.feedback-field:focus {
  outline: none;
  border-color: #3498db;
}

.modal-actions {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  padding: 1.5rem;
  border-top: 1px solid #ecf0f1;
}

.btn-primary {
  background: #3498db;
  color: white;
  border: 2px solid #3498db;
}

.btn-primary:hover:not(:disabled) {
  background: #2980b9;
  border-color: #2980b9;
}

.btn-primary:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-secondary {
  background: transparent;
  color: #7f8c8d;
  border: 2px solid #ecf0f1;
}

.btn-secondary:hover {
  background: #ecf0f1;
  color: #2c3e50;
}
