.grade-analytics-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
}

.grade-analytics-modal {
  background: white;
  border-radius: 12px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
  width: 100%;
  max-width: 1200px;
  max-height: 90vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.modal-header {
  padding: 24px;
  border-bottom: 1px solid #e1e8ed;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #f8f9fa;
}

.modal-header h2 {
  margin: 0;
  color: #2c3e50;
  font-size: 1.5rem;
  font-weight: 600;
}

.close-btn {
  background: none;
  border: none;
  font-size: 24px;
  color: #7f8c8d;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.close-btn:hover {
  background: #e74c3c;
  color: white;
}

.analytics-filters {
  padding: 16px 24px;
  background: #f8f9fa;
  border-bottom: 1px solid #e1e8ed;
  display: flex;
  gap: 16px;
  flex-wrap: wrap;
}

.filter-group {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.filter-group label {
  font-size: 0.9rem;
  font-weight: 500;
  color: #2c3e50;
}

.filter-group select,
.filter-group input {
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 0.9rem;
  min-width: 120px;
}

.analytics-tabs {
  display: flex;
  background: #f8f9fa;
  border-bottom: 1px solid #e1e8ed;
}

.tab-btn {
  padding: 16px 24px;
  background: none;
  border: none;
  font-size: 0.9rem;
  font-weight: 500;
  color: #7f8c8d;
  cursor: pointer;
  transition: all 0.2s ease;
  border-bottom: 3px solid transparent;
}

.tab-btn:hover {
  background: #ecf0f1;
  color: #2c3e50;
}

.tab-btn.active {
  color: #3498db;
  border-bottom-color: #3498db;
  background: white;
}

.analytics-content {
  flex: 1;
  overflow-y: auto;
  padding: 24px;
}

.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
  color: #7f8c8d;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #3498db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 32px;
}

.stat-card {
  background: white;
  border: 1px solid #e1e8ed;
  border-radius: 12px;
  padding: 20px;
  display: flex;
  align-items: center;
  gap: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
}

.stat-icon {
  font-size: 2rem;
  opacity: 0.8;
}

.stat-content h3 {
  margin: 0 0 4px 0;
  font-size: 1.8rem;
  font-weight: 700;
  color: #2c3e50;
}

.stat-content p {
  margin: 0;
  color: #7f8c8d;
  font-size: 0.9rem;
}

.charts-section {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 24px;
}

.chart-container {
  background: white;
  border: 1px solid #e1e8ed;
  border-radius: 12px;
  padding: 20px;
}

.chart-container h3 {
  margin: 0 0 20px 0;
  color: #2c3e50;
  font-size: 1.2rem;
  font-weight: 600;
}

.grade-distribution {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.grade-bar {
  display: grid;
  grid-template-columns: 40px 1fr 120px;
  align-items: center;
  gap: 12px;
}

.grade-label {
  font-weight: 600;
  color: #2c3e50;
  text-align: center;
}

.bar-container {
  background: #f1f3f4;
  border-radius: 8px;
  height: 24px;
  overflow: hidden;
}

.bar-fill {
  height: 100%;
  border-radius: 8px;
  transition: width 0.3s ease;
}

.grade-count {
  font-size: 0.9rem;
  color: #7f8c8d;
  text-align: right;
}

.performance-summary {
  background: white;
  border: 1px solid #e1e8ed;
  border-radius: 12px;
  padding: 20px;
}

.performance-summary h3 {
  margin: 0 0 20px 0;
  color: #2c3e50;
  font-size: 1.2rem;
  font-weight: 600;
}

.summary-stats {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.summary-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f1f3f4;
}

.summary-item:last-child {
  border-bottom: none;
}

.summary-item span:first-child {
  color: #7f8c8d;
  font-size: 0.9rem;
}

.summary-item span:last-child {
  font-weight: 600;
  font-size: 0.9rem;
}

.students-table,
.assignments-table,
.classes-table {
  background: white;
  border: 1px solid #e1e8ed;
  border-radius: 12px;
  overflow: hidden;
}

.table-header {
  display: grid;
  background: #f8f9fa;
  border-bottom: 1px solid #e1e8ed;
  font-weight: 600;
  color: #2c3e50;
  font-size: 0.9rem;
}

.students-table .table-header {
  grid-template-columns: 60px 2fr 1fr 1fr 1fr 1.5fr;
}

.assignments-table .table-header {
  grid-template-columns: 2fr 1fr 1fr 1.5fr 2fr;
}

.classes-table .table-header {
  grid-template-columns: 1fr 1fr 1fr 1fr 2fr;
}

.table-header > div,
.table-row > div {
  padding: 16px 12px;
  display: flex;
  align-items: center;
}

.table-row {
  display: grid;
  border-bottom: 1px solid #f1f3f4;
  transition: background-color 0.2s ease;
  font-size: 0.9rem;
}

.students-table .table-row {
  grid-template-columns: 60px 2fr 1fr 1fr 1fr 1.5fr;
}

.assignments-table .table-row {
  grid-template-columns: 2fr 1fr 1fr 1.5fr 2fr;
}

.classes-table .table-row {
  grid-template-columns: 1fr 1fr 1fr 1fr 2fr;
}

.table-row:hover {
  background: #f8f9fa;
}

.table-row:last-child {
  border-bottom: none;
}

.rank {
  font-weight: 600;
  color: #3498db;
  justify-content: center;
}

.student-info {
  flex-direction: column;
  align-items: flex-start;
  gap: 4px;
}

.student-name {
  font-weight: 600;
  color: #2c3e50;
}

.student-id {
  font-size: 0.8rem;
  color: #7f8c8d;
}

.grade-range {
  font-size: 0.8rem;
}

.assignment-info {
  flex-direction: column;
  align-items: flex-start;
}

.assignment-title {
  font-weight: 600;
  color: #2c3e50;
}

.grade-distribution-mini {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.mini-bar {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.grade-count {
  font-size: 0.8rem;
  padding: 2px 6px;
  border-radius: 4px;
  background: #f1f3f4;
}

.a-grade { background: #d5f4e6; color: #27ae60; }
.b-grade { background: #d4edda; color: #2ecc71; }
.c-grade { background: #fff3cd; color: #f39c12; }
.below-c-grade { background: #f8d7da; color: #e74c3c; }

.class-code {
  font-weight: 600;
  color: #2c3e50;
}

.performance-indicator {
  display: flex;
  align-items: center;
  gap: 12px;
  width: 100%;
}

.performance-bar {
  height: 8px;
  border-radius: 4px;
  max-width: 100px;
}

.modal-footer {
  padding: 20px 24px;
  border-top: 1px solid #e1e8ed;
  display: flex;
  justify-content: flex-end;
  background: #f8f9fa;
}

.btn {
  padding: 10px 20px;
  border: none;
  border-radius: 6px;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn-secondary {
  background: #95a5a6;
  color: white;
}

.btn-secondary:hover {
  background: #7f8c8d;
}

@media (max-width: 768px) {
  .grade-analytics-modal {
    margin: 10px;
    max-width: none;
  }
  
  .analytics-filters {
    flex-direction: column;
    gap: 12px;
  }
  
  .charts-section {
    grid-template-columns: 1fr;
  }
  
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .table-header,
  .table-row {
    font-size: 0.8rem;
  }
  
  .table-header > div,
  .table-row > div {
    padding: 12px 8px;
  }
}
