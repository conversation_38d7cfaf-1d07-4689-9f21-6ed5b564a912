.grade-scale-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
}

.grade-scale-modal {
  background: white;
  border-radius: 12px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
  width: 100%;
  max-width: 800px;
  max-height: 90vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.modal-header {
  padding: 24px;
  border-bottom: 1px solid #e1e8ed;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #f8f9fa;
}

.modal-header h2 {
  margin: 0;
  color: #2c3e50;
  font-size: 1.5rem;
  font-weight: 600;
}

.close-btn {
  background: none;
  border: none;
  font-size: 24px;
  color: #7f8c8d;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.close-btn:hover {
  background: #e74c3c;
  color: white;
}

.modal-body {
  padding: 24px;
  flex: 1;
  overflow-y: auto;
}

.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
  color: #7f8c8d;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #3498db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.grade-scales-container {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.grade-scales-header h3 {
  margin: 0 0 8px 0;
  color: #2c3e50;
  font-size: 1.2rem;
  font-weight: 600;
}

.grade-scales-header p {
  margin: 0;
  color: #7f8c8d;
  font-size: 0.9rem;
}

.grade-scales-table {
  border: 1px solid #e1e8ed;
  border-radius: 8px;
  overflow: hidden;
  background: white;
}

.table-header {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr 100px 100px;
  background: #f8f9fa;
  border-bottom: 1px solid #e1e8ed;
}

.header-cell {
  padding: 16px 12px;
  font-weight: 600;
  color: #2c3e50;
  font-size: 0.9rem;
  text-align: center;
}

.table-row {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr 100px 100px;
  border-bottom: 1px solid #f1f3f4;
  transition: background-color 0.2s ease;
}

.table-row:hover {
  background: #f8f9fa;
}

.table-row:last-child {
  border-bottom: none;
}

.table-cell {
  padding: 16px 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.9rem;
}

.grade-letter-display {
  font-weight: 600;
  color: #2c3e50;
  font-size: 1rem;
}

.grade-input {
  width: 60px;
  padding: 6px 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
  text-align: center;
  font-weight: 600;
  font-size: 0.9rem;
}

.percentage-input {
  width: 80px;
  padding: 6px 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
  text-align: center;
  font-size: 0.9rem;
}

.toggle-switch {
  position: relative;
  display: inline-block;
  width: 50px;
  height: 24px;
}

.toggle-switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.toggle-slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  transition: 0.3s;
  border-radius: 24px;
}

.toggle-slider:before {
  position: absolute;
  content: "";
  height: 18px;
  width: 18px;
  left: 3px;
  bottom: 3px;
  background-color: white;
  transition: 0.3s;
  border-radius: 50%;
}

input:checked + .toggle-slider {
  background-color: #27ae60;
}

input:checked + .toggle-slider:before {
  transform: translateX(26px);
}

.edit-actions {
  display: flex;
  gap: 8px;
}

.edit-btn,
.save-btn,
.cancel-btn {
  background: none;
  border: none;
  font-size: 16px;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.edit-btn:hover {
  background: #3498db;
  transform: scale(1.1);
}

.save-btn:hover {
  background: #27ae60;
  transform: scale(1.1);
}

.cancel-btn:hover {
  background: #e74c3c;
  transform: scale(1.1);
}

.save-btn:disabled,
.cancel-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.grade-scale-info {
  background: #f8f9fa;
  padding: 16px;
  border-radius: 8px;
  border-left: 4px solid #3498db;
}

.grade-scale-info h4 {
  margin: 0 0 12px 0;
  color: #2c3e50;
  font-size: 1rem;
  font-weight: 600;
}

.grade-scale-info ul {
  margin: 0;
  padding-left: 20px;
  color: #7f8c8d;
  font-size: 0.9rem;
}

.grade-scale-info li {
  margin-bottom: 4px;
}

.modal-footer {
  padding: 20px 24px;
  border-top: 1px solid #e1e8ed;
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  background: #f8f9fa;
}

.btn {
  padding: 10px 20px;
  border: none;
  border-radius: 6px;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn-secondary {
  background: #95a5a6;
  color: white;
}

.btn-secondary:hover {
  background: #7f8c8d;
}

.btn-primary {
  background: #3498db;
  color: white;
}

.btn-primary:hover {
  background: #2980b9;
}

@media (max-width: 768px) {
  .grade-scale-modal {
    margin: 10px;
    max-width: none;
  }
  
  .modal-header,
  .modal-body,
  .modal-footer {
    padding: 16px;
  }
  
  .table-header,
  .table-row {
    grid-template-columns: 80px 80px 80px 80px 80px;
    font-size: 0.8rem;
  }
  
  .header-cell,
  .table-cell {
    padding: 12px 8px;
  }
  
  .grade-input,
  .percentage-input {
    width: 60px;
    font-size: 0.8rem;
  }
}
