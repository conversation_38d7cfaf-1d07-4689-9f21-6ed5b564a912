.groups {
  padding: 2rem;
  width: 100%;
  margin: 0;
}

.groups-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  flex-wrap: wrap;
  gap: 1rem;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 1rem;
}

.groups-header h1 {
  color: #2c3e50;
  margin-bottom: 0.5rem;
  font-size: 2.5rem;
  font-weight: 600;
}

.groups-header p {
  color: #7f8c8d;
  font-size: 1.1rem;
}

.create-group-btn {
  background: #3498db;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  white-space: nowrap;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
}

.create-group-btn:hover {
  background: #2980b9;
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(52, 152, 219, 0.3);
}

.create-group-btn:active {
  transform: translateY(0);
}

.groups-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 400px;
  color: #7f8c8d;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #3498db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error-message {
  text-align: center;
  padding: 3rem;
  color: #e74c3c;
}

.error-message h3 {
  color: #c0392b;
  margin-bottom: 1rem;
}

.groups-controls {
  background: white;
  padding: 1.5rem;
  border-radius: 12px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  margin-bottom: 2rem;
  display: flex;
  gap: 1rem;
  align-items: center;
  flex-wrap: wrap;
}

.filter-select {
  padding: 0.75rem;
  border: 2px solid #ecf0f1;
  border-radius: 8px;
  font-size: 1rem;
  background: white;
  color: #2c3e50;
  min-width: 200px;
  transition: border-color 0.3s ease;
}

.filter-select:focus {
  outline: none;
  border-color: #3498db;
  box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

.filter-select option {
  color: #2c3e50;
  background: white;
  padding: 0.5rem;
}

.search-section {
  margin-bottom: 1.5rem;
}

.search-input {
  width: 100%;
  max-width: 400px;
  padding: 0.75rem 1rem;
  border: 2px solid #ecf0f1;
  border-radius: 8px;
  font-size: 1rem;
  transition: border-color 0.3s ease;
}

.search-input:focus {
  outline: none;
  border-color: #3498db;
}

.groups-stats {
  display: flex;
  gap: 2rem;
  flex-wrap: wrap;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 1rem;
  background: #f8f9fa;
  border-radius: 8px;
  min-width: 120px;
}

.stat-number {
  font-size: 2rem;
  font-weight: 700;
  color: #3498db;
  margin-bottom: 0.25rem;
}

.stat-label {
  font-size: 0.9rem;
  color: #7f8c8d;
  text-align: center;
}

.groups-table-container {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  margin-bottom: 2rem;
}

.groups-table {
  width: 100%;
  border-collapse: collapse;
}

.groups-table th {
  background: #f8f9fa;
  padding: 0.75rem 0.5rem;
  text-align: left;
  font-weight: 600;
  color: #2c3e50;
  border-bottom: 2px solid #ecf0f1;
}

.groups-table td {
  padding: 0.75rem 0.5rem;
  border-bottom: 1px solid #ecf0f1;
  vertical-align: middle;
}

.group-row:hover {
  background: #f8f9fa;
}

.group-name .name-cell {
  display: flex;
  flex-direction: column;
}

.group-title {
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 0.25rem;
}

.group-description {
  font-size: 0.9rem;
  color: #7f8c8d;
  font-style: italic;
}

.class-name {
  color: #3498db;
  font-weight: 600;
}

.leader-name {
  color: #2c3e50;
  font-weight: 500;
}

.member-count {
  text-align: center;
}

.count-badge {
  background: #3498db;
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: 600;
}

.created-date {
  color: #7f8c8d;
  font-size: 0.9rem;
}

.group-actions {
  text-align: center;
}

.action-buttons {
  display: flex;
  gap: 0.5rem;
  justify-content: center;
  flex-wrap: wrap;
}

.members-row {
  background: #f8f9fa !important;
}

.members-container {
  padding: 1.5rem;
}

.members-container h4 {
  color: #2c3e50;
  margin-bottom: 1rem;
  font-size: 1.2rem;
}

.members-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1rem;
}

.member-card {
  background: white;
  padding: 1rem;
  border-radius: 8px;
  border: 1px solid #ecf0f1;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.member-info {
  display: flex;
  flex-direction: column;
}

.member-name {
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 0.25rem;
}

.member-id {
  font-size: 0.9rem;
  color: #7f8c8d;
  font-family: monospace;
  margin-bottom: 0.25rem;
}

.member-email {
  font-size: 0.8rem;
  color: #95a5a6;
  font-family: monospace;
}

.leader-badge {
  background: #f39c12;
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
  text-transform: uppercase;
}

.btn {
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.3s ease;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  font-size: 0.9rem;
}

.btn-sm {
  padding: 0.375rem 0.75rem;
  font-size: 0.8rem;
}

.btn-primary {
  background: #3498db;
  color: white;
}

.btn-primary:hover {
  background: #2980b9;
  transform: translateY(-1px);
}

.btn-outline {
  background: transparent;
  border: 2px solid #3498db;
  color: #3498db;
}

.btn-outline:hover {
  background: #3498db;
  color: white;
  transform: translateY(-1px);
}

.btn-secondary {
  background: #95a5a6;
  color: white;
}

.btn-secondary:hover {
  background: #7f8c8d;
  transform: translateY(-1px);
}

.btn-danger {
  background: #e74c3c;
  color: white;
}

.btn-danger:hover {
  background: #c0392b;
  transform: translateY(-1px);
}

.no-groups {
  text-align: center;
  padding: 3rem;
  color: #7f8c8d;
}

.no-groups h3 {
  color: #2c3e50;
  margin-bottom: 0.5rem;
}

.groups-summary {
  text-align: center;
  color: #7f8c8d;
  font-size: 0.9rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .groups {
    padding: 1rem;
  }
  
  .groups-table-container {
    overflow-x: auto;
  }
  
  .groups-table {
    min-width: 800px;
  }
  
  .groups-stats {
    justify-content: center;
  }
  
  .search-input {
    max-width: 100%;
  }
  
  .members-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 480px) {
  .groups-header h1 {
    font-size: 2rem;
  }
  
  .groups-controls {
    padding: 1rem;
  }
  
  .groups-table {
    min-width: 600px;
  }
  
  .groups-stats {
    gap: 1rem;
  }
  
  .stat-item {
    min-width: 100px;
    padding: 0.75rem;
  }
}

/* Modal Styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 1rem;
}

.modal {
  background: white;
  border-radius: 12px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
  max-width: 600px;
  width: 100%;
  max-height: 90vh;
  overflow-y: auto;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  border-bottom: 1px solid #ecf0f1;
}

.modal-header h3 {
  margin: 0;
  color: #2c3e50;
  font-size: 1.5rem;
}

.close-btn {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: #7f8c8d;
  padding: 0;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.close-btn:hover {
  background: #ecf0f1;
  color: #2c3e50;
}

.modal-body {
  padding: 1.5rem;
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 600;
  color: #2c3e50;
}

.form-control {
  width: 100%;
  padding: 0.75rem;
  border: 2px solid #ecf0f1;
  border-radius: 8px;
  font-size: 1rem;
  transition: border-color 0.3s ease;
  box-sizing: border-box;
  background: white;
  color: #2c3e50;
}

.form-control:focus {
  outline: none;
  border-color: #3498db;
  box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

.form-control option {
  color: #2c3e50;
  background: white;
  padding: 0.5rem;
}

.form-control select {
  color: #2c3e50;
  background: white;
}

.form-text {
  font-size: 0.8rem;
  color: #7f8c8d;
  margin-top: 0.25rem;
}

.students-list {
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid #ecf0f1;
  border-radius: 8px;
  padding: 0.5rem;
}

.student-item {
  padding: 0.5rem;
  border-bottom: 1px solid #f8f9fa;
}

.student-item:last-child {
  border-bottom: none;
}

.checkbox-label {
  display: flex;
  align-items: center;
  cursor: pointer;
  gap: 0.75rem;
}

.checkbox-label input[type="checkbox"] {
  width: auto;
  margin: 0;
}

.student-info {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  flex: 1;
}

.student-name {
  font-weight: 600;
  color: #2c3e50;
}

.student-id {
  font-size: 0.9rem;
  color: #7f8c8d;
  font-family: monospace;
}

.no-students {
  text-align: center;
  color: #7f8c8d;
  padding: 2rem;
  font-style: italic;
}

.current-members-list {
  max-height: 200px;
  overflow-y: auto;
  border: 1px solid #ecf0f1;
  border-radius: 8px;
  padding: 0.5rem;
}

.member-item {
  padding: 0.75rem;
  border-bottom: 1px solid #f8f9fa;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.member-item:last-child {
  border-bottom: none;
}

.delete-confirmation {
  text-align: center;
}

.delete-details {
  background: #f8f9fa;
  padding: 1rem;
  border-radius: 8px;
  margin: 1rem 0;
  text-align: left;
}

.warning-text {
  color: #e74c3c;
  font-weight: 600;
  margin-top: 1rem;
}

.modal-actions {
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
  margin-top: 2rem;
  padding-top: 1rem;
  border-top: 1px solid #ecf0f1;
}

/* Enhanced Group Management Styles */
.selection-count {
  color: #3498db;
  font-weight: 600;
  font-size: 0.9rem;
  margin-left: 0.5rem;
  background: #e8f4fd;
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  display: inline-block;
}

.student-count-display {
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  padding: 0.75rem 1rem;
  margin-bottom: 1rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.9rem;
}

.count-info {
  display: flex;
  gap: 1rem;
  align-items: center;
}

.selected-count {
  color: #28a745;
  font-weight: 600;
}

.remaining-slots {
  color: #6c757d;
}

.max-members-highlight {
  background: #fff3cd;
  border: 1px solid #ffeaa7;
  border-radius: 8px;
  padding: 0.75rem;
  margin-bottom: 1rem;
}

.max-members-highlight label {
  color: #856404;
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.max-members-highlight .form-control {
  background: #fffbf0;
  border-color: #ffeaa7;
  font-weight: 600;
  color: #856404;
}

.loading-students {
  text-align: center;
  padding: 2rem;
  color: #7f8c8d;
  font-style: italic;
}

.no-students {
  text-align: center;
  padding: 2rem;
  color: #95a5a6;
  font-style: italic;
  background: #f8f9fa;
  border-radius: 8px;
  border: 2px dashed #dee2e6;
}

.student-controls {
  margin-bottom: 1rem;
}

.student-controls .search-input {
  margin-bottom: 0.75rem;
}

.selection-controls {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.btn-sm {
  padding: 0.375rem 0.75rem;
  font-size: 0.875rem;
  border-radius: 4px;
}

.btn-outline {
  background: transparent;
  border: 1px solid #dee2e6;
  color: #6c757d;
  transition: all 0.2s ease;
}

.btn-outline:hover:not(:disabled) {
  background: #f8f9fa;
  border-color: #adb5bd;
}

.btn-outline:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.students-list {
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  background: #f8f9fa;
}

.student-item {
  border-bottom: 1px solid #e9ecef;
  transition: background-color 0.2s ease;
}

.student-item:last-child {
  border-bottom: none;
}

.student-item:hover {
  background: #e9ecef;
}

.checkbox-label {
  display: flex;
  align-items: center;
  padding: 0.75rem 1rem;
  cursor: pointer;
  margin: 0;
  width: 100%;
}

.checkbox-label input[type="checkbox"] {
  margin-right: 0.75rem;
  transform: scale(1.1);
}

.checkbox-label.disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.checkbox-label.disabled input[type="checkbox"] {
  cursor: not-allowed;
}

/* Member count styling */
.member-count {
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  font-weight: 600;
  font-size: 0.9rem;
  background: #e8f5e8;
  color: #2d5a2d;
}

.member-count.full {
  background: #ffe8e8;
  color: #d63384;
}

/* Action buttons styling */
.action-buttons {
  display: flex;
  gap: 0.5rem;
  align-items: center;
}

.btn {
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 6px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: inline-flex;
  align-items: center;
  gap: 0.25rem;
}

.btn-sm {
  padding: 0.375rem 0.75rem;
  font-size: 0.8rem;
}

.btn-info {
  background: #17a2b8;
  color: white;
}

.btn-info:hover {
  background: #138496;
  transform: translateY(-1px);
}

.btn-danger {
  background: #dc3545;
  color: white;
}

.btn-danger:hover {
  background: #c82333;
  transform: translateY(-1px);
}

/* View modal specific styles */
.group-details {
  margin-bottom: 1.5rem;
}

.detail-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem 0;
  border-bottom: 1px solid #f0f0f0;
}

.detail-row:last-child {
  border-bottom: none;
}

.detail-row strong {
  color: #2c3e50;
  font-weight: 600;
  min-width: 120px;
}

.members-section {
  margin-top: 1.5rem;
  padding-top: 1.5rem;
  border-top: 2px solid #f0f0f0;
}

.members-section h4 {
  color: #2c3e50;
  margin-bottom: 1rem;
  font-size: 1.1rem;
}

.members-list {
  max-height: 200px;
  overflow-y: auto;
}

.member-item {
  padding: 0.75rem;
  background: #f8f9fa;
  border-radius: 8px;
  margin-bottom: 0.5rem;
  border-left: 4px solid #3498db;
}

.member-info {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 500;
}

.leader-badge {
  background: #ffd700;
  color: #856404;
  padding: 0.2rem 0.5rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 600;
  margin-left: 0.5rem;
}

.no-members {
  text-align: center;
  color: #6c757d;
  font-style: italic;
  padding: 1rem;
}

.student-info {
  display: flex;
  flex-direction: column;
  flex: 1;
}

.student-name {
  font-weight: 500;
  color: #2c3e50;
  margin-bottom: 0.25rem;
}

.no-results {
  text-align: center;
  padding: 2rem;
  color: #95a5a6;
  font-style: italic;
}

.error-message {
  color: #e74c3c;
  font-size: 0.875rem;
  margin-top: 0.25rem;
  padding: 0.5rem;
  background: #fdf2f2;
  border-radius: 4px;
  border-left: 3px solid #e74c3c;
}

.form-control.error {
  border-color: #e74c3c;
  box-shadow: 0 0 0 0.2rem rgba(231, 76, 60, 0.25);
}

.modal-actions .btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* Enhanced Member Management Styles */
.current-members {
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 12px;
  max-height: 200px;
  overflow-y: auto;
  background-color: #f9f9f9;
}

.member-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  margin-bottom: 8px;
  background-color: white;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  transition: all 0.2s ease;
}

.member-item:hover {
  border-color: #007bff;
  box-shadow: 0 2px 4px rgba(0, 123, 255, 0.1);
}

.member-info {
  font-weight: 500;
  color: #333;
  flex: 1;
}

.leader-badge {
  color: #28a745;
  font-weight: bold;
  font-size: 0.9em;
}

.no-members {
  text-align: center;
  color: #666;
  font-style: italic;
  margin: 0;
  padding: 20px;
}

.available-students {
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 12px;
  max-height: 200px;
  overflow-y: auto;
  background-color: #f0f8ff;
}

.student-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  margin-bottom: 8px;
  background-color: white;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  transition: all 0.2s ease;
}

.student-item:hover {
  border-color: #28a745;
  box-shadow: 0 2px 4px rgba(40, 167, 69, 0.1);
}

.student-info {
  font-weight: 500;
  color: #333;
  flex: 1;
}

.btn-success {
  background-color: #28a745;
  color: white;
  border: none;
  padding: 4px 8px;
  font-size: 0.875rem;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn-success:hover {
  background-color: #218838;
  transform: translateY(-1px);
}

.btn-warning {
  background-color: #ffc107;
  color: #212529;
  border: none;
}

.btn-warning:hover {
  background-color: #e0a800;
  transform: translateY(-1px);
}

/* Enhanced Pagination Styles */
.pagination-container {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  padding: 1.5rem;
  margin-top: 1rem;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.pagination-info-section {
  display: flex;
  justify-content: center;
  align-items: center;
}

.results-info {
  color: #6c757d;
  font-size: 0.9rem;
  font-weight: 500;
}

.pagination-controls {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 2rem;
  flex-wrap: wrap;
}

.records-per-page {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.9rem;
  color: #495057;
}

.records-per-page label {
  font-weight: 500;
  margin: 0;
}

.records-select {
  padding: 0.375rem 0.75rem;
  border: 1px solid #ced4da;
  border-radius: 6px;
  background: white;
  color: #495057;
  font-size: 0.9rem;
  cursor: pointer;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.records-select:focus {
  outline: none;
  border-color: #3498db;
  box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
}

.page-navigation {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.pagination-btn {
  padding: 0.5rem 0.75rem;
  border: 1px solid #dee2e6;
  background: white;
  color: #495057;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.9rem;
  font-weight: 500;
  transition: all 0.15s ease-in-out;
  min-width: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.pagination-btn:hover:not(:disabled) {
  background: #f8f9fa;
  border-color: #adb5bd;
  color: #212529;
}

.pagination-btn:disabled {
  background: #f8f9fa;
  border-color: #dee2e6;
  color: #6c757d;
  cursor: not-allowed;
  opacity: 0.65;
}

.page-info {
  padding: 0 1rem;
  font-size: 0.9rem;
  color: #495057;
  font-weight: 500;
  white-space: nowrap;
}

.jump-to-page {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.9rem;
  color: #495057;
}

.jump-to-page label {
  font-weight: 500;
  margin: 0;
}

.page-input {
  width: 60px;
  padding: 0.375rem 0.5rem;
  border: 1px solid #ced4da;
  border-radius: 6px;
  background: white;
  color: #495057;
  font-size: 0.9rem;
  text-align: center;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.page-input:focus {
  outline: none;
  border-color: #3498db;
  box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
}

/* Responsive pagination */
@media (max-width: 768px) {
  .pagination-container {
    padding: 1rem;
  }

  .pagination-controls {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }

  .page-navigation {
    justify-content: center;
  }

  .records-per-page,
  .jump-to-page {
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .pagination-controls {
    gap: 0.75rem;
  }

  .page-navigation {
    flex-wrap: wrap;
    gap: 0.25rem;
  }

  .pagination-btn {
    padding: 0.375rem 0.5rem;
    min-width: 35px;
    font-size: 0.8rem;
  }

  .page-info {
    padding: 0 0.5rem;
    font-size: 0.8rem;
  }
}
